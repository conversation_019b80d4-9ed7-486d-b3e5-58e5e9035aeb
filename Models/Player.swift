import Foundation

/// Model voor een tafeltennis speler
struct Player: Identifiable, Codable, Hashable {
    let id = UUID()
    var name: String
    var eloRating: Double
    var matchesPlayed: Int
    var matchesWon: Int
    var gamesPlayed: Int
    var gamesWon: Int
    var createdAt: Date
    
    init(name: String, eloRating: Double = 1200.0) {
        self.name = name
        self.eloRating = eloRating
        self.matchesPlayed = 0
        self.matchesWon = 0
        self.gamesPlayed = 0
        self.gamesWon = 0
        self.createdAt = Date()
    }
    
    /// Berekent het win percentage voor wedstrijden
    var matchWinPercentage: Double {
        guard matchesPlayed > 0 else { return 0.0 }
        return Double(matchesWon) / Double(matchesPlayed) * 100.0
    }
    
    /// Berekent het win percentage voor games
    var gameWinPercentage: Double {
        guard gamesPlayed > 0 else { return 0.0 }
        return Double(gamesWon) / Double(gamesPlayed) * 100.0
    }
    
    /// Berekent het aantal verloren wedstrijden
    var matchesLost: Int {
        return matchesPlayed - matchesWon
    }
    
    /// Berekent het aantal verloren games
    var gamesLost: Int {
        return gamesPlayed - gamesWon
    }
}

/// Extensie voor ELO berekeningen
extension Player {
    /// Berekent de verwachte score tegen een andere speler
    func expectedScore(against opponent: Player) -> Double {
        let ratingDifference = opponent.eloRating - self.eloRating
        return 1.0 / (1.0 + pow(10.0, ratingDifference / 400.0))
    }
    
    /// Berekent de nieuwe ELO rating na een wedstrijd
    func newEloRating(against opponent: Player, actualScore: Double, kFactor: Double = 32.0) -> Double {
        let expectedScore = self.expectedScore(against: opponent)
        return self.eloRating + kFactor * (actualScore - expectedScore)
    }
}
