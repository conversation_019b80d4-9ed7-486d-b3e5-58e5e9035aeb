import Foundation

/// Enum voor teams
enum Team: String, CaseIterable, Codable {
    case team1 = "team1"
    case team2 = "team2"
    
    var displayName: String {
        switch self {
        case .team1: return "Team 1"
        case .team2: return "Team 2"
        }
    }
    
    var opposite: Team {
        switch self {
        case .team1: return .team2
        case .team2: return .team1
        }
    }
}

/// Model voor een individuele game binnen een wedstrijd
struct Game: Identifiable, Codable {
    let id = UUID()
    var gameNumber: Int
    var team1Score: Int = 0
    var team2Score: Int = 0
    var winner: Team?
    var isCompleted: Bool = false
    var startedAt: Date
    var completedAt: Date?
    
    // Service tracking
    var currentServer: Team = .team1
    var currentServerPlayer: Int = 1 // 1 of 2 voor dubbels
    var serviceChangesCount: Int = 0
    
    // Score history voor undo/redo
    var scoreHistory: [ScorePoint] = []
    
    init(gameNumber: Int) {
        self.gameNumber = gameNumber
        self.startedAt = Date()
    }
    
    /// Controleert of de game in deuce is (10-10 of hoger met gelijk aantal punten)
    var isDeuce: Bool {
        return team1Score >= 10 && team2Score >= 10 && team1Score == team2Score
    }
    
    /// Controleert of een team heeft gewonnen
    var hasWinner: Bool {
        let minScore = 11
        let minDifference = 2
        
        if team1Score >= minScore && team1Score - team2Score >= minDifference {
            return true
        }
        if team2Score >= minScore && team2Score - team1Score >= minDifference {
            return true
        }
        return false
    }
    
    /// Bepaalt de winnaar van de game
    mutating func determineWinner() {
        if hasWinner {
            winner = team1Score > team2Score ? .team1 : .team2
            isCompleted = true
            completedAt = Date()
        }
    }
    
    /// Voegt een punt toe aan een team
    mutating func addPoint(to team: Team, scoringPlayer: Int = 1) {
        let scorePoint = ScorePoint(
            team: team,
            scoringPlayer: scoringPlayer,
            team1ScoreBefore: team1Score,
            team2ScoreBefore: team2Score,
            serverBefore: currentServer,
            serverPlayerBefore: currentServerPlayer,
            timestamp: Date()
        )
        
        scoreHistory.append(scorePoint)
        
        // Update score
        if team == .team1 {
            team1Score += 1
        } else {
            team2Score += 1
        }
        
        // Check for winner
        determineWinner()
        
        // Update service if game is not completed
        if !isCompleted {
            updateService()
        }
    }
    
    /// Update service volgens tafeltennis regels
    mutating func updateService() {
        serviceChangesCount += 1
        
        if isDeuce {
            // Bij deuce wisselt service na elk punt
            currentServer = currentServer.opposite
            // Bij dubbels wisselt ook de speler
            currentServerPlayer = currentServerPlayer == 1 ? 2 : 1
        } else {
            // Normale service wisseling: elke 2 punten
            if serviceChangesCount % 2 == 0 {
                currentServer = currentServer.opposite
                // Bij dubbels wisselt ook de speler
                currentServerPlayer = currentServerPlayer == 1 ? 2 : 1
            }
        }
    }
    
    /// Ongedaan maken van het laatste punt
    mutating func undoLastPoint() -> Bool {
        guard let lastPoint = scoreHistory.popLast() else { return false }
        
        // Herstel scores
        team1Score = lastPoint.team1ScoreBefore
        team2Score = lastPoint.team2ScoreBefore
        
        // Herstel service
        currentServer = lastPoint.serverBefore
        currentServerPlayer = lastPoint.serverPlayerBefore
        serviceChangesCount -= 1
        
        // Reset winner status
        winner = nil
        isCompleted = false
        completedAt = nil
        
        return true
    }
}

/// Model voor een individueel punt in de score geschiedenis
struct ScorePoint: Identifiable, Codable {
    let id = UUID()
    let team: Team
    let scoringPlayer: Int
    let team1ScoreBefore: Int
    let team2ScoreBefore: Int
    let serverBefore: Team
    let serverPlayerBefore: Int
    let timestamp: Date
}
