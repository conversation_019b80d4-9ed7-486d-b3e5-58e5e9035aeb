import SwiftUI

struct MatchDetailView: View {
    @EnvironmentObject var dataManager: DataManager
    let match: Match
    @State private var showingLiveMatch = false
    @State private var showingEditMatch = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Match Header
                MatchDetailHeaderView(match: match)
                
                // Match Status and Actions
                MatchActionsView(
                    match: match,
                    onPlayLive: { showingLiveMatch = true },
                    onEdit: { showingEditMatch = true }
                )
                
                // Games Overview
                if !match.games.isEmpty {
                    GamesOverviewView(match: match)
                }
                
                // Player Statistics for this match
                if match.status == .completed {
                    MatchPlayerStatsView(match: match)
                }
                
                // Match Timeline
                MatchTimelineView(match: match)
            }
            .padding()
        }
        .navigationTitle("Wedstrijd Details")
        .navigationBarTitleDisplayMode(.inline)
        .fullScreenCover(isPresented: $showingLiveMatch) {
            NavigationView {
                LiveMatchView(match: match)
            }
        }
        .sheet(isPresented: $showingEditMatch) {
            EditMatchView(match: match)
        }
    }
}

struct MatchDetailHeaderView: View {
    let match: Match
    
    var body: some View {
        VStack(spacing: 16) {
            // Match Type Badge
            HStack {
                Label(match.type.displayName, systemImage: match.type == .singles ? "person" : "person.2")
                    .font(.subheadline)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.blue.opacity(0.2))
                    .cornerRadius(8)
                
                Spacer()
                
                Text("Best of \(match.bestOfGames)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // Teams
            HStack(spacing: 20) {
                // Team 1
                VStack(spacing: 8) {
                    Text("Team 1")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    VStack(spacing: 4) {
                        Text(match.team1Player1.name)
                            .font(.headline)
                            .fontWeight(.bold)
                        
                        if let team1Player2 = match.team1Player2 {
                            Text(team1Player2.name)
                                .font(.subheadline)
                        }
                    }
                    
                    if match.status == .completed || match.status == .inProgress {
                        Text("\(match.team1GamesWon)")
                            .font(.system(size: 36, weight: .bold, design: .rounded))
                            .foregroundColor(.blue)
                    }
                }
                
                Text("vs")
                    .font(.title2)
                    .foregroundColor(.secondary)
                
                // Team 2
                VStack(spacing: 8) {
                    Text("Team 2")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    VStack(spacing: 4) {
                        Text(match.team2Player1.name)
                            .font(.headline)
                            .fontWeight(.bold)
                        
                        if let team2Player2 = match.team2Player2 {
                            Text(team2Player2.name)
                                .font(.subheadline)
                        }
                    }
                    
                    if match.status == .completed || match.status == .inProgress {
                        Text("\(match.team2GamesWon)")
                            .font(.system(size: 36, weight: .bold, design: .rounded))
                            .foregroundColor(.red)
                    }
                }
            }
            
            // Winner Badge
            if match.status == .completed {
                if match.type == .mixMatch {
                    // Voor Mix & Match: toon de daadwerkelijke winnaar(s)
                    let winners = match.mixMatchWinners
                    if winners.count == 1 {
                        Text("\(winners[0].name) Wint!")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 8)
                            .background(Color.green)
                            .cornerRadius(20)
                    } else if winners.count > 1 {
                        let winnerNames = winners.map { $0.name }.joined(separator: " & ")
                        Text("\(winnerNames) Delen de Winst!")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 8)
                            .background(Color.green)
                            .cornerRadius(20)
                    }
                } else if let winner = match.winner {
                    Text("\(winner.displayName) Wint!")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 8)
                        .background(Color.green)
                        .cornerRadius(20)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct MatchActionsView: View {
    let match: Match
    let onPlayLive: () -> Void
    let onEdit: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                // Status Badge
                Label(match.status.displayName, systemImage: statusIcon)
                    .font(.subheadline)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(statusColor.opacity(0.2))
                    .foregroundColor(statusColor)
                    .cornerRadius(8)
                
                Spacer()
                
                // Action Buttons
                HStack(spacing: 12) {
                    if match.status == .scheduled || match.status == .inProgress {
                        Button("Live Spelen") {
                            onPlayLive()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    
                    Button("Bewerken") {
                        onEdit()
                    }
                    .buttonStyle(.bordered)
                }
            }
        }
    }
    
    private var statusIcon: String {
        switch match.status {
        case .scheduled: return "calendar"
        case .inProgress: return "play.circle"
        case .completed: return "checkmark.circle"
        case .cancelled: return "xmark.circle"
        }
    }
    
    private var statusColor: Color {
        switch match.status {
        case .scheduled: return .blue
        case .inProgress: return .orange
        case .completed: return .green
        case .cancelled: return .red
        }
    }
}

struct GamesOverviewView: View {
    let match: Match
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Games Overzicht")
                .font(.headline)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(match.games) { game in
                    GameSummaryCard(game: game)
                }
            }
        }
    }
}

struct GameSummaryCard: View {
    let game: Game
    
    var body: some View {
        VStack(spacing: 8) {
            Text("Game \(game.gameNumber)")
                .font(.caption)
                .fontWeight(.medium)
            
            Text("\(game.team1Score) - \(game.team2Score)")
                .font(.title3)
                .fontWeight(.bold)
            
            if game.isCompleted, let winner = game.winner {
                Circle()
                    .fill(winner == .team1 ? Color.blue : Color.red)
                    .frame(width: 8, height: 8)
            } else {
                Circle()
                    .fill(Color.orange)
                    .frame(width: 8, height: 8)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct MatchPlayerStatsView: View {
    let match: Match
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Speler Statistieken")
                .font(.headline)
            
            VStack(spacing: 8) {
                ForEach(match.allPlayers) { player in
                    PlayerMatchStatsRow(player: player, match: match)
                }
            }
        }
    }
}

struct PlayerMatchStatsRow: View {
    let player: Player
    let match: Match
    
    var gamesWon: Int {
        return match.games.filter { game in
            guard let winner = game.winner else { return false }
            
            if match.type == .singles {
                return (player.id == match.team1Player1.id && winner == .team1) ||
                       (player.id == match.team2Player1.id && winner == .team2)
            } else {
                let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
                return (isTeam1 && winner == .team1) || (!isTeam1 && winner == .team2)
            }
        }.count
    }
    
    var totalPoints: Int {
        return match.games.reduce(0) { total, game in
            if match.type == .singles {
                if player.id == match.team1Player1.id {
                    return total + game.team1Score
                } else if player.id == match.team2Player1.id {
                    return total + game.team2Score
                }
            } else {
                let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
                return total + (isTeam1 ? game.team1Score : game.team2Score)
            }
            return total
        }
    }
    
    var body: some View {
        HStack {
            Text(player.name)
                .font(.subheadline)
                .fontWeight(.medium)
            
            Spacer()
            
            HStack(spacing: 16) {
                VStack(alignment: .trailing, spacing: 2) {
                    Text("Games")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    Text("\(gamesWon)/\(match.games.count)")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text("Punten")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    Text("\(totalPoints)")
                        .font(.caption)
                        .fontWeight(.medium)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct MatchTimelineView: View {
    let match: Match
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Tijdlijn")
                .font(.headline)
            
            VStack(alignment: .leading, spacing: 8) {
                TimelineItem(
                    title: "Wedstrijd Aangemaakt",
                    time: match.createdAt,
                    icon: "plus.circle",
                    color: .blue
                )
                
                if match.status == .inProgress || match.status == .completed {
                    TimelineItem(
                        title: "Wedstrijd Gestart",
                        time: match.games.first?.startedAt ?? match.createdAt,
                        icon: "play.circle",
                        color: .orange
                    )
                }
                
                if let completedAt = match.completedAt {
                    TimelineItem(
                        title: "Wedstrijd Voltooid",
                        time: completedAt,
                        icon: "checkmark.circle",
                        color: .green
                    )
                }
            }
        }
    }
}

struct TimelineItem: View {
    let title: String
    let time: Date
    let icon: String
    let color: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(time, formatter: dateTimeFormatter)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
    
    private var dateTimeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter
    }
}

#Preview {
    NavigationView {
        MatchDetailView(match: Match(
            type: .singles,
            team1Player1: Player(name: "Speler 1"),
            team2Player1: Player(name: "Speler 2")
        ))
    }
    .environmentObject(DataManager())
}
