import SwiftUI

struct LiveMatchView: View {
    @EnvironmentObject var dataManager: DataManager
    @Environment(\.presentationMode) var presentationMode
    
    @State private var match: Match
    @State private var currentGameIndex: Int = 0
    @State private var showingGameCompleteAlert = false
    @State private var showingMatchCompleteAlert = false
    @State private var showingCancelAlert = false
    @State private var notifications: [String] = []
    
    init(match: Match) {
        self._match = State(initialValue: match)
        self._currentGameIndex = State(initialValue: max(0, match.games.count - 1))
    }
    
    var currentGame: Game? {
        guard currentGameIndex < match.games.count else { return nil }
        return match.games[currentGameIndex]
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Match Header
            MatchHeaderView(match: match)
            
            // Current Game Score
            if let game = currentGame {
                GameScoreView(
                    game: game,
                    match: match,
                    onTeam1Point: { addPoint(to: .team1) },
                    onTeam2Point: { addPoint(to: .team2) },
                    onUndo: { undoLastPoint() }
                )
            }
            
            // Service Indicator
            if let game = currentGame, !game.isCompleted {
                ServiceIndicatorView(game: game, match: match)
            }
            
            // Game History
            if match.games.count > 1 {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(Array(match.games.enumerated()), id: \.offset) { index, game in
                            GameHistoryCard(
                                game: game,
                                isCurrentGame: index == currentGameIndex
                            )
                        }
                    }
                    .padding(.horizontal)
                }
            }

            Spacer()

            // Control Buttons
            HStack(spacing: 20) {
                Button("Undo") {
                    undoLastPoint()
                }
                .disabled(!canUndoPoint())
                .padding()
                .background(canUndoPoint() ? Color.orange : Color.gray)
                .foregroundColor(.white)
                .cornerRadius(8)

                Button("Annuleren") {
                    showingCancelAlert = true
                }
                .padding()
                .background(Color.red)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .padding()

            // Notifications
            if !notifications.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    ForEach(notifications, id: \.self) { notification in
                        Text(notification)
                            .font(.caption)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(Color.blue.opacity(0.2))
                            .cornerRadius(8)
                    }
                }
                .padding()
                .onTapGesture {
                    notifications.removeAll()
                }
            }
        }
        .navigationTitle("Live Wedstrijd")
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .alert("Game Voltooid", isPresented: $showingGameCompleteAlert) {
            Button("Volgende Game") {
                startNextGame()
            }
        } message: {
            if let game = currentGame, let winner = game.winner {
                Text("Game \(game.gameNumber) gewonnen door \(winner.displayName)!")
            }
        }
        .alert("Wedstrijd Voltooid", isPresented: $showingMatchCompleteAlert) {
            Button("Afsluiten") {
                completeMatch()
            }
        } message: {
            if let winner = match.winner {
                Text("Wedstrijd gewonnen door \(winner.displayName)!")
            }
        }
        .alert("Wedstrijd Annuleren", isPresented: $showingCancelAlert) {
            Button("Annuleren", role: .destructive) {
                cancelMatch()
            }
            Button("Doorgaan", role: .cancel) { }
        } message: {
            Text("Weet je zeker dat je deze wedstrijd wilt annuleren? Alle voortgang gaat verloren.")
        }
    }
    
    // MARK: - Game Logic
    
    private func addPoint(to team: Team) {
        guard currentGameIndex < match.games.count,
              !match.games[currentGameIndex].isCompleted else { return }

        print("⚽ ADD POINT: Adding point for \(team) to game \(currentGameIndex + 1)")

        // Voeg punt toe aan huidige game
        match.games[currentGameIndex].addPoint(to: team, matchType: match.type)

        // Play point sound effect
        SoundManager.shared.playPointSound()

        print("⚽ ADD POINT: Game \(currentGameIndex + 1) score is now \(match.games[currentGameIndex].team1Score)-\(match.games[currentGameIndex].team2Score)")

        // Update match in data manager
        dataManager.updateMatch(match)

        // Update local match state to reflect the changes
        if let updatedMatch = dataManager.matches.first(where: { $0.id == match.id }) {
            match = updatedMatch
        }

        // Check voor game completion
        if match.games[currentGameIndex].isCompleted {
            handleGameCompletion()
        }

        // Update notifications
        updateNotifications()
    }
    
    private func undoLastPoint() {
        guard currentGameIndex < match.games.count else { return }

        print("🔄 UNDO: Attempting to undo from game \(currentGameIndex + 1)")
        let success = match.games[currentGameIndex].undoLastPoint()
        if success {
            print("🔄 UNDO: Successfully undid point from game \(currentGameIndex + 1)")
            dataManager.updateMatch(match)

            // Update local match state to reflect the changes
            if let updatedMatch = dataManager.matches.first(where: { $0.id == match.id }) {
                match = updatedMatch
            }

            updateNotifications()
        } else {
            print("🔄 UNDO: Failed to undo point from game \(currentGameIndex + 1)")
        }
    }
    
    private func canUndoPoint() -> Bool {
        guard let game = currentGame else { return false }
        return !game.scoreHistory.isEmpty
    }
    
    private func handleGameCompletion() {
        // Check of wedstrijd voltooid is
        if match.isCompleted {
            showingMatchCompleteAlert = true
        } else {
            showingGameCompleteAlert = true
        }
    }
    
    private func startNextGame() {
        let nextGameNumber = match.games.count + 1
        let newGame = GameRulesEngine.createNewGame(gameNumber: nextGameNumber, matchType: match.type)
        
        match.games.append(newGame)
        currentGameIndex = match.games.count - 1
        
        dataManager.updateMatch(match)
    }
    
    private func completeMatch() {
        match.status = .completed
        match.completedAt = Date()
        dataManager.updateMatch(match)
        presentationMode.wrappedValue.dismiss()
    }
    
    private func cancelMatch() {
        match.status = .cancelled
        dataManager.updateMatch(match)
        presentationMode.wrappedValue.dismiss()
    }
    
    private func updateNotifications() {
        notifications.removeAll()
        
        if let game = currentGame {
            notifications.append(contentsOf: GameRulesEngine.gameEventNotifications(for: game))
        }
        
        notifications.append(contentsOf: GameRulesEngine.matchEventNotifications(for: match))
    }
}

struct MatchHeaderView: View {
    let match: Match
    
    var body: some View {
        VStack(spacing: 8) {
            Text(match.description)
                .font(.headline)
                .multilineTextAlignment(.center)
            
            HStack {
                Text(match.type.displayName)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.2))
                    .cornerRadius(4)
                
                Spacer()
                
                Text("Best of \(match.bestOfGames)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("Games: \(match.team1GamesWon) - \(match.team2GamesWon)")
                    .font(.caption)
                    .fontWeight(.medium)
            }
        }
        .padding()
        .background(Color(.systemGray6))
    }
}

struct GameScoreView: View {
    let game: Game
    let match: Match
    let onTeam1Point: () -> Void
    let onTeam2Point: () -> Void
    let onUndo: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            // Game number
            Text("Game \(game.gameNumber)")
                .font(.title2)
                .fontWeight(.bold)
            
            // Score display
            HStack(spacing: 40) {
                // Team 1
                VStack(spacing: 12) {
                    Text(teamName(for: .team1))
                        .font(.headline)
                        .multilineTextAlignment(.center)
                    
                    Text("\(game.team1Score)")
                        .font(.system(size: 60, weight: .bold, design: .rounded))
                        .foregroundColor(.blue)
                    
                    Button(action: onTeam1Point) {
                        Image(systemName: "plus.circle.fill")
                            .font(.title)
                            .foregroundColor(.blue)
                    }
                    .disabled(game.isCompleted)
                }
                
                Text(":")
                    .font(.system(size: 40, weight: .bold))
                    .foregroundColor(.secondary)
                
                // Team 2
                VStack(spacing: 12) {
                    Text(teamName(for: .team2))
                        .font(.headline)
                        .multilineTextAlignment(.center)
                    
                    Text("\(game.team2Score)")
                        .font(.system(size: 60, weight: .bold, design: .rounded))
                        .foregroundColor(.red)
                    
                    Button(action: onTeam2Point) {
                        Image(systemName: "plus.circle.fill")
                            .font(.title)
                            .foregroundColor(.red)
                    }
                    .disabled(game.isCompleted)
                }
            }
            
            // Game status
            if game.isCompleted {
                if let winner = game.winner {
                    Text("Game gewonnen door \(winner.displayName)")
                        .font(.headline)
                        .foregroundColor(.green)
                        .padding()
                        .background(Color.green.opacity(0.2))
                        .cornerRadius(8)
                }
            } else if game.isDeuce {
                Text("DEUCE - Service wisselt na elk punt")
                    .font(.subheadline)
                    .foregroundColor(.orange)
                    .padding()
                    .background(Color.orange.opacity(0.2))
                    .cornerRadius(8)
            }
        }
        .padding()
    }
    
    private func teamName(for team: Team) -> String {
        switch team {
        case .team1:
            if match.type == .singles {
                return match.team1Player1.name
            } else {
                return "\(match.team1Player1.name)\n\(match.team1Player2?.name ?? "")"
            }
        case .team2:
            if match.type == .singles {
                return match.team2Player1.name
            } else {
                return "\(match.team2Player1.name)\n\(match.team2Player2?.name ?? "")"
            }
        }
    }
}

struct ServiceIndicatorView: View {
    let game: Game
    let match: Match
    
    var body: some View {
        VStack(spacing: 8) {
            Text("Service")
                .font(.headline)
            
            HStack {
                ServicePlayerView(
                    playerName: serverName,
                    isServing: true,
                    teamColor: game.currentServer == .team1 ? .blue : .red
                )
                
                Spacer()
                
                Text("→")
                    .font(.title2)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                ServicePlayerView(
                    playerName: receiverName,
                    isServing: false,
                    teamColor: game.currentServer == .team1 ? .red : .blue
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
    }
    
    private var serverName: String {
        switch (game.currentServer, game.currentServerPlayer) {
        case (.team1, 1):
            return match.team1Player1.name
        case (.team1, 2):
            return match.team1Player2?.name ?? ""
        case (.team2, 1):
            return match.team2Player1.name
        case (.team2, 2):
            return match.team2Player2?.name ?? ""
        default:
            return ""
        }
    }
    
    private var receiverName: String {
        let receivingTeam = game.currentServer.opposite
        // Voor enkelspel is het simpel, voor dubbels complexer
        if match.type == .singles {
            return receivingTeam == .team1 ? match.team1Player1.name : match.team2Player1.name
        } else {
            // Bij dubbels is de ontvanger de andere speler van het ontvangende team
            switch (receivingTeam, game.currentServerPlayer) {
            case (.team1, 1):
                return match.team1Player2?.name ?? ""
            case (.team1, 2):
                return match.team1Player1.name
            case (.team2, 1):
                return match.team2Player2?.name ?? ""
            case (.team2, 2):
                return match.team2Player1.name
            default:
                return ""
            }
        }
    }
}

struct ServicePlayerView: View {
    let playerName: String
    let isServing: Bool
    let teamColor: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(playerName)
                .font(.subheadline)
                .fontWeight(isServing ? .bold : .regular)
            
            Circle()
                .fill(isServing ? teamColor : Color.clear)
                .stroke(teamColor, lineWidth: 2)
                .frame(width: 12, height: 12)
        }
    }
}

struct GameHistoryCard: View {
    let game: Game
    let isCurrentGame: Bool

    var body: some View {
        VStack(spacing: 4) {
            Text("Game \(game.gameNumber)")
                .font(.caption2)
                .fontWeight(.medium)

            Text("\(game.team1Score) - \(game.team2Score)")
                .font(.caption)
                .fontWeight(isCurrentGame ? .bold : .regular)

            if game.isCompleted, let winner = game.winner {
                Circle()
                    .fill(winner == .team1 ? Color.blue : Color.red)
                    .frame(width: 6, height: 6)
            } else if isCurrentGame {
                Circle()
                    .fill(Color.orange)
                    .frame(width: 6, height: 6)
            } else {
                Circle()
                    .fill(Color.clear)
                    .frame(width: 6, height: 6)
            }
        }
        .padding(8)
        .background(isCurrentGame ? Color.blue.opacity(0.2) : Color(.systemGray6))
        .cornerRadius(8)
    }
}

#Preview {
    NavigationView {
        LiveMatchView(match: Match(
            type: .singles,
            team1Player1: Player(name: "Speler 1"),
            team2Player1: Player(name: "Speler 2")
        ))
    }
    .environmentObject(DataManager())
}
