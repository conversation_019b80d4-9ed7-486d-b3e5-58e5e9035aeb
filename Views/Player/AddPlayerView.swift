import SwiftUI

struct AddPlayerView: View {
    @EnvironmentObject var dataManager: DataManager
    @Environment(\.presentationMode) var presentationMode
    
    @State private var playerName = ""
    @State private var initialEloRating = 1200.0
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Speler Informatie")) {
                    TextField("Naam", text: $playerName)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Beginnende ELO Rating: \(Int(initialEloRating))")
                            .font(.subheadline)
                        
                        Slider(value: $initialEloRating, in: 800...2000, step: 50)
                        
                        HStack {
                            Text("800")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Text("1200 (Standaard)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Text("2000")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Section(footer: Text("ELO rating wordt automatisch aangepast op basis van wedstrijdresultaten.")) {
                    EmptyView()
                }
            }
            .navigationTitle("Nieuwe Speler")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Annuleren") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Opslaan") {
                        savePlayer()
                    }
                    .disabled(playerName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Fout"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }
    
    private func savePlayer() {
        let trimmedName = playerName.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Validatie
        guard !trimmedName.isEmpty else {
            alertMessage = "Voer een geldige naam in."
            showingAlert = true
            return
        }
        
        // Controleer of speler al bestaat
        if dataManager.players.contains(where: { $0.name.lowercased() == trimmedName.lowercased() }) {
            alertMessage = "Een speler met deze naam bestaat al."
            showingAlert = true
            return
        }
        
        // Maak nieuwe speler aan
        let newPlayer = Player(name: trimmedName, eloRating: initialEloRating)
        dataManager.addPlayer(newPlayer)
        
        presentationMode.wrappedValue.dismiss()
    }
}

#Preview {
    AddPlayerView()
        .environmentObject(DataManager())
}
