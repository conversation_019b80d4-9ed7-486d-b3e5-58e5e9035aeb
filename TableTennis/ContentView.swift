//
//  ContentView.swift
//  Table Tennis
//
//  Created by <PERSON><PERSON> on 18/06/2025.
//

import SwiftUI

struct ContentView: View {
    @EnvironmentObject var dataManager: DataManager
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var flicManager: FlicManager
    @State private var selectedTab = 0
    @State private var showingSettings = false

    var body: some View {
        TabView(selection: $selectedTab) {
            NavigationStack {
                MatchListView(showingSettings: $showingSettings)
            }
            .tabItem {
                Image(systemName: "list.bullet")
                Text("Matches")
            }
            .tag(0)

            NavigationStack {
                PlayerListView(showingSettings: $showingSettings)
            }
            .tabItem {
                Image(systemName: "person.2")
                Text("Players")
            }
            .tag(1)

            NavigationStack {
                StatisticsView(showingSettings: $showingSettings)
            }
            .tabItem {
                Image(systemName: "chart.bar")
                Text("Statistics")
            }
            .tag(2)
        }
        .sheet(isPresented: $showingSettings) {
            SettingsView()
        }
    }
}

#Preview {
    ContentView()
        .environmentObject(DataManager())
        .environmentObject(ThemeManager())
        .environmentObject(FlicManager())
}
