import Foundation

/// Manager voor het berekenen van uitgebreide statistieken
class StatisticsManager: ObservableObject {
    
    /// Berekent uitgebreide statistieken voor een speler
    /// - Parameters:
    ///   - player: <PERSON>
    ///   - matches: Alle wed<PERSON><PERSON><PERSON><PERSON>
    /// - Returns: PlayerStatistics object met alle statistieken
    static func calculatePlayerStatistics(for player: Player, matches: [Match]) -> PlayerStatistics {
        let playerMatches = matches.filter { match in
            match.allPlayers.contains { $0.id == player.id }
        }
        
        let completedMatches = playerMatches.filter { $0.status == .completed }
        
        return PlayerStatistics(
            player: player,
            totalMatches: completedMatches.count,
            matchesWon: calculateMatchesWon(for: player, in: completedMatches),
            totalGames: calculateTotalGames(for: player, in: completedMatches),
            gamesWon: calculateGamesWon(for: player, in: completedMatches),
            totalPoints: calculateTotalPoints(for: player, in: completedMatches),
            averagePointsPerGame: calculateAveragePointsPerGame(for: player, in: completedMatches),
            longestWinStreak: calculateLongestWinStreak(for: player, in: completedMatches),
            currentWinStreak: calculateCurrentWinStreak(for: player, in: completedMatches),
            singlesRecord: calculateSinglesRecord(for: player, in: completedMatches),
            doublesRecord: calculateDoublesRecord(for: player, in: completedMatches),
            eloHistory: calculateEloHistory(for: player, in: completedMatches),
            recentForm: calculateRecentForm(for: player, in: completedMatches),
            favoritePartners: calculateFavoritePartners(for: player, in: completedMatches),
            toughestOpponents: calculateToughestOpponents(for: player, in: completedMatches)
        )
    }
    
    /// Berekent algemene app statistieken
    /// - Parameters:
    ///   - players: Alle spelers
    ///   - matches: Alle wedstrijden
    /// - Returns: AppStatistics object
    static func calculateAppStatistics(players: [Player], matches: [Match]) -> AppStatistics {
        let completedMatches = matches.filter { $0.status == .completed }
        
        return AppStatistics(
            totalPlayers: players.count,
            totalMatches: completedMatches.count,
            totalGames: completedMatches.flatMap { $0.games }.filter { $0.isCompleted }.count,
            singlesMatches: completedMatches.filter { $0.type == .singles }.count,
            doublesMatches: completedMatches.filter { $0.type == .doubles }.count,
            averageMatchDuration: calculateAverageMatchDuration(matches: completedMatches),
            mostActivePlayer: players.max { $0.matchesPlayed < $1.matchesPlayed },
            highestRatedPlayer: players.max { $0.eloRating < $1.eloRating },
            mostImprovedPlayer: calculateMostImprovedPlayer(players: players, matches: completedMatches),
            longestMatch: calculateLongestMatch(matches: completedMatches),
            shortestMatch: calculateShortestMatch(matches: completedMatches)
        )
    }
    
    // MARK: - Private Helper Methods
    
    private static func calculateMatchesWon(for player: Player, in matches: [Match]) -> Int {
        return matches.filter { match in
            guard let winner = match.winner else { return false }
            
            if match.type == .singles {
                return (player.id == match.team1Player1.id && winner == .team1) ||
                       (player.id == match.team2Player1.id && winner == .team2)
            } else {
                let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
                return (isTeam1 && winner == .team1) || (!isTeam1 && winner == .team2)
            }
        }.count
    }
    
    private static func calculateTotalGames(for player: Player, in matches: [Match]) -> Int {
        var totalGames = 0

        for match in matches {
            if match.type == .mixMatch {
                // Voor Mix & Match: tel alleen games waarin deze speler daadwerkelijk speelde
                for game in match.games {
                    guard game.isCompleted,
                          let team1Players = game.mixMatchTeam1Players,
                          let team2Players = game.mixMatchTeam2Players else { continue }

                    let playerInTeam1 = team1Players.contains(where: { $0.id == player.id })
                    let playerInTeam2 = team2Players.contains(where: { $0.id == player.id })

                    if playerInTeam1 || playerInTeam2 {
                        totalGames += 1
                    }
                }
            } else {
                // Voor Singles en Doubles: tel alle games van de match
                totalGames += match.games.filter { $0.isCompleted }.count
            }
        }

        return totalGames
    }
    
    private static func calculateGamesWon(for player: Player, in matches: [Match]) -> Int {
        var gamesWon = 0

        for match in matches {
            if match.type == .mixMatch {
                // Voor Mix & Match: tel alleen games waarin deze speler daadwerkelijk speelde en won
                for game in match.games {
                    guard game.isCompleted,
                          let gameWinner = game.winner,
                          let team1Players = game.mixMatchTeam1Players,
                          let team2Players = game.mixMatchTeam2Players else { continue }

                    let playerInTeam1 = team1Players.contains(where: { $0.id == player.id })
                    let playerInTeam2 = team2Players.contains(where: { $0.id == player.id })

                    // Speler speelde in deze game en won
                    if (playerInTeam1 && gameWinner == .team1) || (playerInTeam2 && gameWinner == .team2) {
                        gamesWon += 1
                    }
                }
            } else {
                // Voor Singles en Doubles: originele logica
                for game in match.games {
                    guard let winner = game.winner else { continue }

                    if match.type == .singles {
                        if (player.id == match.team1Player1.id && winner == .team1) ||
                           (player.id == match.team2Player1.id && winner == .team2) {
                            gamesWon += 1
                        }
                    } else {
                        let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
                        if (isTeam1 && winner == .team1) || (!isTeam1 && winner == .team2) {
                            gamesWon += 1
                        }
                    }
                }
            }
        }

        return gamesWon
    }
    
    private static func calculateTotalPoints(for player: Player, in matches: [Match]) -> Int {
        return matches.flatMap { match in
            match.games.map { game in
                if match.type == .singles {
                    if player.id == match.team1Player1.id {
                        return game.team1Score
                    } else if player.id == match.team2Player1.id {
                        return game.team2Score
                    }
                } else {
                    let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
                    return isTeam1 ? game.team1Score : game.team2Score
                }
                return 0
            }
        }.reduce(0, +)
    }
    
    private static func calculateAveragePointsPerGame(for player: Player, in matches: [Match]) -> Double {
        let totalGames = calculateTotalGames(for: player, in: matches)
        guard totalGames > 0 else { return 0.0 }
        
        let totalPoints = calculateTotalPoints(for: player, in: matches)
        return Double(totalPoints) / Double(totalGames)
    }
    
    private static func calculateLongestWinStreak(for player: Player, in matches: [Match]) -> Int {
        let sortedMatches = matches.sorted { $0.completedAt ?? Date.distantPast < $1.completedAt ?? Date.distantPast }
        
        var longestStreak = 0
        var currentStreak = 0
        
        for match in sortedMatches {
            let playerWon = didPlayerWin(player: player, match: match)
            
            if playerWon {
                currentStreak += 1
                longestStreak = max(longestStreak, currentStreak)
            } else {
                currentStreak = 0
            }
        }
        
        return longestStreak
    }
    
    private static func calculateCurrentWinStreak(for player: Player, in matches: [Match]) -> Int {
        let sortedMatches = matches.sorted { $0.completedAt ?? Date.distantPast > $1.completedAt ?? Date.distantPast }
        
        var currentStreak = 0
        
        for match in sortedMatches {
            let playerWon = didPlayerWin(player: player, match: match)
            
            if playerWon {
                currentStreak += 1
            } else {
                break
            }
        }
        
        return currentStreak
    }
    
    private static func calculateSinglesRecord(for player: Player, in matches: [Match]) -> (wins: Int, losses: Int) {
        let singlesMatches = matches.filter { $0.type == .singles }
        let wins = singlesMatches.filter { didPlayerWin(player: player, match: $0) }.count
        let losses = singlesMatches.count - wins
        return (wins, losses)
    }
    
    private static func calculateDoublesRecord(for player: Player, in matches: [Match]) -> (wins: Int, losses: Int) {
        let doublesMatches = matches.filter { $0.type == .doubles }
        let wins = doublesMatches.filter { didPlayerWin(player: player, match: $0) }.count
        let losses = doublesMatches.count - wins
        return (wins, losses)
    }
    
    private static func calculateEloHistory(for player: Player, in matches: [Match]) -> [EloHistoryPoint] {
        // Placeholder - zou ELO geschiedenis moeten bijhouden
        return []
    }
    
    private static func calculateRecentForm(for player: Player, in matches: [Match]) -> [Bool] {
        let recentMatches = matches
            .sorted { $0.completedAt ?? Date.distantPast > $1.completedAt ?? Date.distantPast }
            .prefix(10)
        
        return recentMatches.map { didPlayerWin(player: player, match: $0) }
    }
    
    private static func calculateFavoritePartners(for player: Player, in matches: [Match]) -> [(Player, Int)] {
        let doublesMatches = matches.filter { $0.type == .doubles }
        var partnerCounts: [UUID: (Player, Int)] = [:]
        
        for match in doublesMatches {
            if let partner = getPartner(for: player, in: match) {
                if let existing = partnerCounts[partner.id] {
                    partnerCounts[partner.id] = (partner, existing.1 + 1)
                } else {
                    partnerCounts[partner.id] = (partner, 1)
                }
            }
        }
        
        return partnerCounts.values
            .sorted { $0.1 > $1.1 }
            .map { ($0.0, $0.1) }
    }
    
    private static func calculateToughestOpponents(for player: Player, in matches: [Match]) -> [(Player, Int, Int)] {
        var opponentRecords: [UUID: (Player, Int, Int)] = [:] // (opponent, wins, losses)
        
        for match in matches {
            let opponents = getOpponents(for: player, in: match)
            let playerWon = didPlayerWin(player: player, match: match)
            
            for opponent in opponents {
                if let existing = opponentRecords[opponent.id] {
                    let newWins = playerWon ? existing.1 + 1 : existing.1
                    let newLosses = playerWon ? existing.2 : existing.2 + 1
                    opponentRecords[opponent.id] = (opponent, newWins, newLosses)
                } else {
                    let wins = playerWon ? 1 : 0
                    let losses = playerWon ? 0 : 1
                    opponentRecords[opponent.id] = (opponent, wins, losses)
                }
            }
        }
        
        return opponentRecords.values
            .filter { $0.1 + $0.2 >= 3 } // Minimaal 3 wedstrijden
            .sorted { $0.2 > $1.2 } // Sorteer op aantal verliezen
            .map { ($0.0, $0.1, $0.2) }
    }
    
    private static func calculateAverageMatchDuration(matches: [Match]) -> TimeInterval {
        let durationsInSeconds = matches.compactMap { match -> TimeInterval? in
            guard let completedAt = match.completedAt else { return nil }
            return completedAt.timeIntervalSince(match.createdAt)
        }
        
        guard !durationsInSeconds.isEmpty else { return 0 }
        return durationsInSeconds.reduce(0, +) / Double(durationsInSeconds.count)
    }
    
    private static func calculateMostImprovedPlayer(players: [Player], matches: [Match]) -> Player? {
        // Placeholder - zou ELO verbetering over tijd moeten berekenen
        return players.max { $0.eloRating < $1.eloRating }
    }
    
    private static func calculateLongestMatch(matches: [Match]) -> Match? {
        return matches.max { match1, match2 in
            let duration1 = match1.completedAt?.timeIntervalSince(match1.createdAt) ?? 0
            let duration2 = match2.completedAt?.timeIntervalSince(match2.createdAt) ?? 0
            return duration1 < duration2
        }
    }
    
    private static func calculateShortestMatch(matches: [Match]) -> Match? {
        return matches.min { match1, match2 in
            let duration1 = match1.completedAt?.timeIntervalSince(match1.createdAt) ?? TimeInterval.greatestFiniteMagnitude
            let duration2 = match2.completedAt?.timeIntervalSince(match2.createdAt) ?? TimeInterval.greatestFiniteMagnitude
            return duration1 < duration2
        }
    }
    
    // MARK: - Utility Methods
    
    private static func didPlayerWin(player: Player, match: Match) -> Bool {
        guard let winner = match.winner else { return false }
        
        if match.type == .singles {
            return (player.id == match.team1Player1.id && winner == .team1) ||
                   (player.id == match.team2Player1.id && winner == .team2)
        } else {
            let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
            return (isTeam1 && winner == .team1) || (!isTeam1 && winner == .team2)
        }
    }
    
    private static func getPartner(for player: Player, in match: Match) -> Player? {
        guard match.type == .doubles else { return nil }
        
        if player.id == match.team1Player1.id {
            return match.team1Player2
        } else if player.id == match.team1Player2?.id {
            return match.team1Player1
        } else if player.id == match.team2Player1.id {
            return match.team2Player2
        } else if player.id == match.team2Player2?.id {
            return match.team2Player1
        }
        
        return nil
    }
    
    private static func getOpponents(for player: Player, in match: Match) -> [Player] {
        if match.type == .singles {
            if player.id == match.team1Player1.id {
                return [match.team2Player1]
            } else {
                return [match.team1Player1]
            }
        } else {
            let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
            
            if isTeam1 {
                return [match.team2Player1, match.team2Player2].compactMap { $0 }
            } else {
                return [match.team1Player1, match.team1Player2].compactMap { $0 }
            }
        }
    }
}

// MARK: - Statistics Data Models

struct PlayerStatistics {
    let player: Player
    let totalMatches: Int
    let matchesWon: Int
    let totalGames: Int
    let gamesWon: Int
    let totalPoints: Int
    let averagePointsPerGame: Double
    let longestWinStreak: Int
    let currentWinStreak: Int
    let singlesRecord: (wins: Int, losses: Int)
    let doublesRecord: (wins: Int, losses: Int)
    let eloHistory: [EloHistoryPoint]
    let recentForm: [Bool] // true = win, false = loss
    let favoritePartners: [(Player, Int)] // (partner, games played together)
    let toughestOpponents: [(Player, Int, Int)] // (opponent, wins, losses)
    
    var matchWinPercentage: Double {
        guard totalMatches > 0 else { return 0.0 }
        return Double(matchesWon) / Double(totalMatches) * 100.0
    }
    
    var gameWinPercentage: Double {
        guard totalGames > 0 else { return 0.0 }
        return Double(gamesWon) / Double(totalGames) * 100.0
    }
}

struct AppStatistics {
    let totalPlayers: Int
    let totalMatches: Int
    let totalGames: Int
    let singlesMatches: Int
    let doublesMatches: Int
    let averageMatchDuration: TimeInterval
    let mostActivePlayer: Player?
    let highestRatedPlayer: Player?
    let mostImprovedPlayer: Player?
    let longestMatch: Match?
    let shortestMatch: Match?
}

struct EloHistoryPoint {
    let date: Date
    let rating: Double
    let change: Double
    let opponent: String
    let result: String
}
