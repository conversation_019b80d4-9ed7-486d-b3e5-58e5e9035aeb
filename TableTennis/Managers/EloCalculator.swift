import Foundation

/// Calculator voor ELO rating berekeningen
struct EloCalculator {
    
    /// Standaard K-factor voor ELO berekeningen
    static let defaultKFactor: Double = 32.0
    
    /// Berekent de verwachte score voor een speler tegen een tegenstander
    /// - Parameters:
    ///   - playerRating: ELO rating van de speler
    ///   - opponentRating: ELO rating van de tegenstander
    /// - Returns: Verwachte score tussen 0.0 en 1.0
    static func expectedScore(playerRating: Double, opponentRating: Double) -> Double {
        let ratingDifference = opponentRating - playerRating
        return 1.0 / (1.0 + pow(10.0, ratingDifference / 400.0))
    }
    
    /// Berekent de nieuwe ELO rating na een wedstrijd
    /// - Parameters:
    ///   - currentRating: Huidige ELO rating van de speler
    ///   - opponentRating: ELO rating van de tegenstander
    ///   - actualScore: Werkelijke score (1.0 voor winst, 0.5 voor gelijkspel, 0.0 voor verlies)
    ///   - kFactor: K-factor voor de berekening (standaard 32.0)
    /// - Returns: Nieuwe ELO rating
    static func newRating(currentRating: Double, 
                         opponentRating: Double, 
                         actualScore: Double, 
                         kFactor: Double = defaultKFactor) -> Double {
        let expectedScore = expectedScore(playerRating: currentRating, opponentRating: opponentRating)
        return currentRating + kFactor * (actualScore - expectedScore)
    }
    
    /// Berekent ELO wijzigingen voor een enkelspel wedstrijd
    /// - Parameters:
    ///   - player1: Eerste speler
    ///   - player2: Tweede speler
    ///   - winner: Winnaar van de wedstrijd (.team1 of .team2)
    ///   - kFactor: K-factor voor de berekening
    /// - Returns: Tuple met nieuwe ratings voor beide spelers
    static func calculateSinglesMatch(player1: Player, 
                                    player2: Player, 
                                    winner: Team, 
                                    kFactor: Double = defaultKFactor) -> (player1NewRating: Double, player2NewRating: Double) {
        let player1Score: Double = winner == .team1 ? 1.0 : 0.0
        let player2Score: Double = winner == .team2 ? 1.0 : 0.0
        
        let player1NewRating = newRating(
            currentRating: player1.eloRating,
            opponentRating: player2.eloRating,
            actualScore: player1Score,
            kFactor: kFactor
        )
        
        let player2NewRating = newRating(
            currentRating: player2.eloRating,
            opponentRating: player1.eloRating,
            actualScore: player2Score,
            kFactor: kFactor
        )
        
        return (player1NewRating, player2NewRating)
    }
    
    /// Berekent ELO wijzigingen voor een dubbelspel wedstrijd
    /// - Parameters:
    ///   - team1Player1: Eerste speler van team 1
    ///   - team1Player2: Tweede speler van team 1
    ///   - team2Player1: Eerste speler van team 2
    ///   - team2Player2: Tweede speler van team 2
    ///   - winner: Winnaar van de wedstrijd (.team1 of .team2)
    ///   - kFactor: K-factor voor de berekening
    /// - Returns: Tuple met nieuwe ratings voor alle vier spelers
    static func calculateDoublesMatch(team1Player1: Player,
                                    team1Player2: Player,
                                    team2Player1: Player,
                                    team2Player2: Player,
                                    winner: Team,
                                    kFactor: Double = defaultKFactor) -> (team1Player1NewRating: Double, 
                                                                         team1Player2NewRating: Double,
                                                                         team2Player1NewRating: Double,
                                                                         team2Player2NewRating: Double) {
        
        // Bereken gemiddelde team ratings
        let team1AverageRating = (team1Player1.eloRating + team1Player2.eloRating) / 2.0
        let team2AverageRating = (team2Player1.eloRating + team2Player2.eloRating) / 2.0
        
        // Bepaal scores
        let team1Score: Double = winner == .team1 ? 1.0 : 0.0
        let team2Score: Double = winner == .team2 ? 1.0 : 0.0
        
        // Bereken nieuwe ratings voor team 1 spelers
        let team1Player1NewRating = newRating(
            currentRating: team1Player1.eloRating,
            opponentRating: team2AverageRating,
            actualScore: team1Score,
            kFactor: kFactor
        )
        
        let team1Player2NewRating = newRating(
            currentRating: team1Player2.eloRating,
            opponentRating: team2AverageRating,
            actualScore: team1Score,
            kFactor: kFactor
        )
        
        // Bereken nieuwe ratings voor team 2 spelers
        let team2Player1NewRating = newRating(
            currentRating: team2Player1.eloRating,
            opponentRating: team1AverageRating,
            actualScore: team2Score,
            kFactor: kFactor
        )
        
        let team2Player2NewRating = newRating(
            currentRating: team2Player2.eloRating,
            opponentRating: team1AverageRating,
            actualScore: team2Score,
            kFactor: kFactor
        )
        
        return (team1Player1NewRating, team1Player2NewRating, team2Player1NewRating, team2Player2NewRating)
    }
    
    /// Berekent de rating wijziging voor een speler
    /// - Parameters:
    ///   - currentRating: Huidige rating
    ///   - opponentRating: Tegenstander rating
    ///   - actualScore: Werkelijke score
    ///   - kFactor: K-factor
    /// - Returns: Rating wijziging (positief voor verbetering, negatief voor verslechtering)
    static func ratingChange(currentRating: Double,
                           opponentRating: Double,
                           actualScore: Double,
                           kFactor: Double = defaultKFactor) -> Double {
        let expectedScore = expectedScore(playerRating: currentRating, opponentRating: opponentRating)
        return kFactor * (actualScore - expectedScore)
    }

    /// Berekent ELO wijzigingen voor een Mix & Match game gebaseerd op punten gescoord
    /// - Parameters:
    ///   - team1Players: Spelers van team 1 voor deze specifieke game
    ///   - team2Players: Spelers van team 2 voor deze specifieke game
    ///   - team1Score: Punten gescoord door team 1
    ///   - team2Score: Punten gescoord door team 2
    ///   - kFactor: K-factor voor de berekening (standaard aangepast voor Mix & Match)
    /// - Returns: Dictionary met nieuwe ratings per speler ID
    static func calculateMixMatchGame(team1Players: [Player],
                                    team2Players: [Player],
                                    team1Score: Int,
                                    team2Score: Int,
                                    kFactor: Double = defaultKFactor * 0.75) -> [UUID: Double] {

        guard team1Players.count == 2 && team2Players.count == 2 else {
            return [:]
        }

        // Bereken gemiddelde team ratings
        let team1AverageRating = (team1Players[0].eloRating + team1Players[1].eloRating) / 2.0
        let team2AverageRating = (team2Players[0].eloRating + team2Players[1].eloRating) / 2.0

        // Bereken score ratio gebaseerd op daadwerkelijke punten
        let totalPoints = team1Score + team2Score
        guard totalPoints > 0 else { return [:] }

        let team1ScoreRatio = Double(team1Score) / Double(totalPoints)
        let team2ScoreRatio = Double(team2Score) / Double(totalPoints)

        var newRatings: [UUID: Double] = [:]

        // Bereken nieuwe ratings voor team 1 spelers
        for player in team1Players {
            let newRating = newRating(
                currentRating: player.eloRating,
                opponentRating: team2AverageRating,
                actualScore: team1ScoreRatio,
                kFactor: kFactor
            )
            newRatings[player.id] = newRating
        }

        // Bereken nieuwe ratings voor team 2 spelers
        for player in team2Players {
            let newRating = newRating(
                currentRating: player.eloRating,
                opponentRating: team1AverageRating,
                actualScore: team2ScoreRatio,
                kFactor: kFactor
            )
            newRatings[player.id] = newRating
        }

        return newRatings
    }
    
    /// Bepaalt de K-factor op basis van speler eigenschappen
    /// - Parameters:
    ///   - player: De speler
    ///   - isImportantMatch: Of dit een belangrijke wedstrijd is
    /// - Returns: Aangepaste K-factor
    static func adaptiveKFactor(for player: Player, isImportantMatch: Bool = false) -> Double {
        var kFactor = defaultKFactor
        
        // Nieuwe spelers krijgen hogere K-factor voor snellere aanpassing
        if player.matchesPlayed < 10 {
            kFactor = 40.0
        } else if player.matchesPlayed < 30 {
            kFactor = 36.0
        }
        
        // Zeer ervaren spelers krijgen lagere K-factor voor stabiliteit
        if player.matchesPlayed > 100 {
            kFactor = 24.0
        }
        
        // Hoge rated spelers krijgen lagere K-factor
        if player.eloRating > 1800 {
            kFactor *= 0.8
        }
        
        // Belangrijke wedstrijden krijgen hogere K-factor
        if isImportantMatch {
            kFactor *= 1.2
        }
        
        return kFactor
    }
    
    /// Berekent de win probability tussen twee spelers
    /// - Parameters:
    ///   - player1Rating: Rating van speler 1
    ///   - player2Rating: Rating van speler 2
    /// - Returns: Kans dat speler 1 wint (0.0 tot 1.0)
    static func winProbability(player1Rating: Double, player2Rating: Double) -> Double {
        return expectedScore(playerRating: player1Rating, opponentRating: player2Rating)
    }
    
    /// Geeft een tekstuele beschrijving van de rating
    /// - Parameter rating: ELO rating
    /// - Returns: Beschrijving van het niveau
    static func ratingDescription(for rating: Double) -> String {
        switch rating {
        case 2200...:
            return "Wereldklasse"
        case 2000..<2200:
            return "Master"
        case 1800..<2000:
            return "Expert"
        case 1600..<1800:
            return "Gevorderd"
        case 1400..<1600:
            return "Gemiddeld"
        case 1200..<1400:
            return "Beginner"
        case 1000..<1200:
            return "Nieuwkomer"
        default:
            return "Ongerangschikt"
        }
    }
    
    /// Berekent de rating kleur voor UI weergave
    /// - Parameter rating: ELO rating
    /// - Returns: Kleur gebaseerd op rating niveau
    static func ratingColor(for rating: Double) -> String {
        switch rating {
        case 2000...:
            return "purple"
        case 1800..<2000:
            return "gold"
        case 1600..<1800:
            return "orange"
        case 1400..<1600:
            return "blue"
        case 1200..<1400:
            return "green"
        default:
            return "gray"
        }
    }
}
