import AVFoundation
import Foundation

class SoundManager: ObservableObject {
    static let shared = SoundManager()

    private var pointPlayer: AVAudioPlayer?
    private var gameWinPlayer: AVAudioPlayer?
    private var matchWinPlayer: AVAudioPlayer?

    private init() {
        setupAudioSession()
        setupAudioPlayers()
    }
    
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try AVAudioSession.sharedInstance().setActive(true)
            print("🔊 Audio session setup successful")
        } catch {
            print("🔊 Failed to setup audio session: \(error)")
        }
    }

    private func setupAudioPlayers() {
        // Setup point sound player
        if let pointURL = Bundle.main.url(forResource: "point", withExtension: "wav") {
            do {
                pointPlayer = try AVAudioPlayer(contentsOf: pointURL)
                pointPlayer?.prepareToPlay()
                print("🔊 Point sound loaded successfully")
            } catch {
                print("🔊 Failed to load point sound: \(error)")
            }
        } else {
            print("🔊 Point sound file not found")
        }

        // Setup game win sound player
        if let gameWinURL = Bundle.main.url(forResource: "game_win", withExtension: "wav") {
            do {
                gameWinPlayer = try AVAudioPlayer(contentsOf: gameWinURL)
                gameWinPlayer?.prepareToPlay()
                print("🔊 Game win sound loaded successfully")
            } catch {
                print("🔊 Failed to load game win sound: \(error)")
            }
        } else {
            print("🔊 Game win sound file not found")
        }

        // Setup match win sound player
        if let matchWinURL = Bundle.main.url(forResource: "match_win", withExtension: "wav") {
            do {
                matchWinPlayer = try AVAudioPlayer(contentsOf: matchWinURL)
                matchWinPlayer?.prepareToPlay()
                print("🔊 Match win sound loaded successfully")
            } catch {
                print("🔊 Failed to load match win sound: \(error)")
            }
        } else {
            print("🔊 Match win sound file not found")
        }
    }

    func playPointSound() {
        print("🔊 Playing point sound (WAV)...")

        guard let player = pointPlayer else {
            print("🔊 Point player not available")
            return
        }

        // Ensure audio session is active
        ensureAudioSessionActive()

        // Reset to beginning and play
        player.currentTime = 0
        let success = player.play()

        print("🔊 Point sound play result: \(success)")
    }
    
    func playGameWinSound() {
        print("🔊 Playing game win sound (WAV)...")

        guard let player = gameWinPlayer else {
            print("🔊 Game win player not available")
            return
        }

        ensureAudioSessionActive()

        // Reset to beginning and play
        player.currentTime = 0
        let success = player.play()

        print("🔊 Game win sound play result: \(success)")
    }

    func playMatchWinSound() {
        print("🔊 Playing match win sound (WAV)...")

        guard let player = matchWinPlayer else {
            print("🔊 Match win player not available")
            return
        }

        ensureAudioSessionActive()

        // Reset to beginning and play
        player.currentTime = 0
        let success = player.play()

        print("🔊 Match win sound play result: \(success)")
    }

    private func ensureAudioSessionActive() {
        do {
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("🔊 Failed to activate audio session: \(error)")
        }
    }
}
