import SwiftUI
import Foundation

/// Enum voor beschikbare thema's
enum AppTheme: String, CaseIterable {
    case light = "light"
    case dark = "dark"
    case system = "system"
    
    var displayName: String {
        switch self {
        case .light:
            return "Light"
        case .dark:
            return "Dark"
        case .system:
            return "System"
        }
    }
    
    var icon: String {
        switch self {
        case .light:
            return "sun.max"
        case .dark:
            return "moon"
        case .system:
            return "gear"
        }
    }
    
    var colorScheme: ColorScheme? {
        switch self {
        case .light:
            return .light
        case .dark:
            return .dark
        case .system:
            return nil
        }
    }
}

/// Manager voor thema instellingen
class ThemeManager: ObservableObject {
    @Published var currentTheme: AppTheme {
        didSet {
            saveTheme()
        }
    }
    
    private let userDefaults = UserDefaults.standard
    private let themeKey = "selectedTheme"
    
    init() {
        // Laad opgeslagen thema of gebruik systeem als default
        let savedTheme = userDefaults.string(forKey: themeKey) ?? AppTheme.system.rawValue
        self.currentTheme = AppTheme(rawValue: savedTheme) ?? .system
    }
    
    /// Sla het huidige thema op
    private func saveTheme() {
        userDefaults.set(currentTheme.rawValue, forKey: themeKey)
        print("🎨 Theme saved: \(currentTheme.displayName)")
    }
    
    /// Wissel naar een specifiek thema
    func setTheme(_ theme: AppTheme) {
        currentTheme = theme
    }
    
    /// Reset naar systeem thema
    func resetToSystem() {
        currentTheme = .system
    }
}
