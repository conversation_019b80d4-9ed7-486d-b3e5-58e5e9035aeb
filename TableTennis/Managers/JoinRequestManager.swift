import Foundation
import SwiftUI

/// Manager voor het bij<PERSON><PERSON> van join requests en badge counts
@MainActor
class JoinRequestManager: ObservableObject {
    @Published var pendingRequestsCount: Int = 0
    @Published var isLoading: Bool = false
    
    private let cloudKitManager: CloudKitManager
    private var currentCompetition: Competition?
    
    init(cloudKitManager: CloudKitManager) {
        self.cloudKitManager = cloudKitManager
    }
    
    /// Start het monitoren van join requests voor een competitie
    func startMonitoring(for competition: Competition) {
        guard currentCompetition?.id != competition.id else { return }
        
        currentCompetition = competition
        Task {
            await loadPendingRequestsCount()
        }
    }
    
    /// Stop het monitoren
    func stopMonitoring() {
        currentCompetition = nil
        pendingRequestsCount = 0
    }
    
    /// Laadt het aantal openstaande aanvragen
    func loadPendingRequestsCount() async {
        guard let competition = currentCompetition else {
            await MainActor.run {
                pendingRequestsCount = 0
            }
            return
        }
        
        // Controleer of de gebruiker eigenaar is
        let isOwner = await cloudKitManager.isCurrentUserOwner(of: competition)
        guard isOwner else {
            await MainActor.run {
                pendingRequestsCount = 0
            }
            return
        }
        
        await MainActor.run {
            isLoading = true
        }
        
        do {
            let requests = try await cloudKitManager.getJoinRequestsForCompetition(competition)

            // Haal responses op om te bepalen welke requests nog pending zijn
            let requestIds = requests.map { $0.id }
            let responses = try await cloudKitManager.getJoinRequestResponses(for: requestIds)
            let responseRequestIds = Set(responses.map { $0.joinRequestId })

            // Tel alleen requests die nog geen response hebben
            let pendingCount = requests.filter { !responseRequestIds.contains($0.id) }.count

            await MainActor.run {
                self.pendingRequestsCount = pendingCount
                self.isLoading = false
                print("📊 JoinRequestManager: Found \(pendingCount) pending requests")
            }
        } catch {
            await MainActor.run {
                self.pendingRequestsCount = 0
                self.isLoading = false
                print("❌ JoinRequestManager: Error loading requests: \(error)")
            }
        }
    }
    
    /// Refresh de count (bijvoorbeeld na het goedkeuren/afwijzen van een request)
    func refreshCount() {
        Task {
            await loadPendingRequestsCount()
        }
    }
    
    /// Vermindert de count met 1 (voor optimistische updates)
    func decrementCount() {
        if pendingRequestsCount > 0 {
            pendingRequestsCount -= 1
        }
    }
    
    /// Verhoogt de count met 1 (voor optimistische updates)
    func incrementCount() {
        pendingRequestsCount += 1
    }
}
