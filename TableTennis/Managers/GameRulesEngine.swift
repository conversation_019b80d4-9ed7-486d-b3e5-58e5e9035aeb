import Foundation

/// Engine voor het handhaven van tafeltennis regels
class GameRulesEngine: ObservableObject {
    
    // MARK: - Service Rules
    
    /// Bepaalt wie er moet serveren bij het begin van een game
    static func initialServer(for gameNumber: Int) -> Team {
        // Afwisselend beginnen: oneven games team1, even games team2
        return gameNumber % 2 == 1 ? .team1 : .team2
    }
    
    /// Bepaalt welke speler van een team moet serveren bij dubbels
    static func initialServerPlayer(for gameNumber: Int, team: Team) -> Int {
        // Bij dubbels wisselt ook de startende speler per game
        return gameNumber % 2 == 1 ? 1 : 2
    }
    
    /// Controleert of service moet wisselen na een punt
    static func shouldChangeService(currentScore: (team1: Int, team2: Int), 
                                   serviceChanges: Int) -> Bool {
        let totalPoints = currentScore.team1 + currentScore.team2
        
        // Bij deuce (10-10 of hoger met gelijke stand) wisselt service na elk punt
        if currentScore.team1 >= 10 && currentScore.team2 >= 10 && 
           currentScore.team1 == currentScore.team2 {
            return true
        }
        
        // Normale regel: service wisselt elke 2 punten
        return totalPoints % 2 == 0 && totalPoints > 0
    }
    
    /// Bepaalt de volgende server bij dubbels volgens officiële regels
    static func nextDoublesServer(currentServer: Team, 
                                 currentServerPlayer: Int,
                                 gameNumber: Int) -> (team: Team, player: Int) {
        // Bij dubbels rotatie: Team1-Speler1 -> Team2-Speler1 -> Team1-Speler2 -> Team2-Speler2
        switch (currentServer, currentServerPlayer) {
        case (.team1, 1):
            return (.team2, 1)
        case (.team2, 1):
            return (.team1, 2)
        case (.team1, 2):
            return (.team2, 2)
        case (.team2, 2):
            return (.team1, 1)
        default:
            return (.team1, 1)
        }
    }
    
    // MARK: - Scoring Rules
    
    /// Controleert of een game is gewonnen
    static func isGameWon(score: (team1: Int, team2: Int)) -> Bool {
        let minScore = 11
        let minDifference = 2
        
        return (score.team1 >= minScore && score.team1 - score.team2 >= minDifference) ||
               (score.team2 >= minScore && score.team2 - score.team1 >= minDifference)
    }
    
    /// Bepaalt de winnaar van een game
    static func gameWinner(score: (team1: Int, team2: Int)) -> Team? {
        guard isGameWon(score: score) else { return nil }
        return score.team1 > score.team2 ? .team1 : .team2
    }
    
    /// Controleert of een wedstrijd is gewonnen
    static func isMatchWon(gamesWon: (team1: Int, team2: Int), bestOf: Int) -> Bool {
        let gamesNeeded = (bestOf / 2) + 1
        return gamesWon.team1 >= gamesNeeded || gamesWon.team2 >= gamesNeeded
    }
    
    /// Bepaalt de winnaar van een wedstrijd
    static func matchWinner(gamesWon: (team1: Int, team2: Int), bestOf: Int) -> Team? {
        guard isMatchWon(gamesWon: gamesWon, bestOf: bestOf) else { return nil }
        return gamesWon.team1 > gamesWon.team2 ? .team1 : .team2
    }
    
    // MARK: - Game State Validation
    
    /// Valideert of een score geldig is
    static func isValidScore(team1Score: Int, team2Score: Int) -> Bool {
        // Scores kunnen niet negatief zijn
        guard team1Score >= 0 && team2Score >= 0 else { return false }
        
        // Als een team 11+ punten heeft, moet het verschil minimaal 2 zijn of de andere moet ook 10+ hebben
        if team1Score >= 11 {
            return team2Score >= 10 || team1Score - team2Score >= 2
        }
        if team2Score >= 11 {
            return team1Score >= 10 || team2Score - team1Score >= 2
        }
        
        return true
    }
    
    /// Controleert of een wedstrijd configuratie geldig is
    static func isValidMatchConfiguration(type: MatchType,
                                        team1Player1: Player,
                                        team1Player2: Player?,
                                        team2Player1: Player,
                                        team2Player2: Player?) -> Bool {
        switch type {
        case .singles:
            // Bij enkelspel mogen er geen tweede spelers zijn
            return team1Player2 == nil && team2Player2 == nil
        case .doubles:
            // Bij dubbelspel moeten beide teams 2 spelers hebben
            return team1Player2 != nil && team2Player2 != nil
        case .mixMatch:
            // Mix & Match heeft eigen validatie logica
            return true
        }
    }
    
    // MARK: - Match Flow
    
    /// Bepaalt of er een nieuwe game moet worden gestart
    static func shouldStartNewGame(currentGames: [Game], bestOf: Int) -> Bool {
        let completedGames = currentGames.filter { $0.isCompleted }
        let gamesWon = (
            team1: completedGames.filter { $0.winner == .team1 }.count,
            team2: completedGames.filter { $0.winner == .team2 }.count
        )
        
        return !isMatchWon(gamesWon: gamesWon, bestOf: bestOf)
    }
    
    /// Creëert een nieuwe game met juiste service instellingen
    static func createNewGame(gameNumber: Int, matchType: MatchType) -> Game {
        var game = Game(gameNumber: gameNumber)
        game.currentServer = initialServer(for: gameNumber)
        game.currentServerPlayer = initialServerPlayer(for: gameNumber, team: game.currentServer)
        return game
    }

    /// Creëert een nieuwe Mix & Match game met specifieke team samenstelling
    static func createMixMatchGame(gameNumber: Int, team1Players: [Player], team2Players: [Player]) -> Game {
        var game = Game(gameNumber: gameNumber)
        game.mixMatchTeam1Players = team1Players
        game.mixMatchTeam2Players = team2Players
        game.currentServer = initialServer(for: gameNumber)
        game.currentServerPlayer = initialServerPlayer(for: gameNumber, team: game.currentServer)
        return game
    }
    
    // MARK: - Notifications
    
    /// Genereert notificaties voor belangrijke game events
    static func gameEventNotifications(for game: Game) -> [String] {
        var notifications: [String] = []
        
        // Deuce notificatie
        if game.isDeuce {
            notifications.append("Deuce! Service wisselt nu na elk punt.")
        }
        
        // Game point notificatie
        let team1GamePoint = game.team1Score >= 10 && game.team1Score - game.team2Score >= 1
        let team2GamePoint = game.team2Score >= 10 && game.team2Score - game.team1Score >= 1
        
        if team1GamePoint && !game.isDeuce {
            notifications.append("Game point voor Team 1!")
        } else if team2GamePoint && !game.isDeuce {
            notifications.append("Game point voor Team 2!")
        }
        
        // Game gewonnen notificatie
        if game.isCompleted, let winner = game.winner {
            notifications.append("Game gewonnen door \(winner.displayName)!")
        }
        
        return notifications
    }
    
    /// Genereert notificaties voor wedstrijd events
    static func matchEventNotifications(for match: Match) -> [String] {
        var notifications: [String] = []
        
        // Match point notificatie
        let gamesNeeded = (match.bestOfGames / 2) + 1
        let team1MatchPoint = match.team1GamesWon == gamesNeeded - 1
        let team2MatchPoint = match.team2GamesWon == gamesNeeded - 1
        
        if team1MatchPoint {
            notifications.append("Match point voor Team 1!")
        } else if team2MatchPoint {
            notifications.append("Match point voor Team 2!")
        }
        
        // Match gewonnen notificatie
        if match.isCompleted, let winner = match.winner {
            notifications.append("Wedstrijd gewonnen door \(winner.displayName)!")
        }
        
        return notifications
    }

    // MARK: - Game Management

    /// Bepaalt welke game er volgende gespeeld moet worden
    static func getNextGameToPlay(for match: Match) -> Game? {
        if match.type == .mixMatch {
            // Voor Mix & Match: vind de eerste niet-voltooide game
            return match.games.first { !$0.isCompleted }
        } else {
            // Voor reguliere wedstrijden: vind de eerste game zonder scores
            // of de laatste game als die nog niet voltooid is
            if let gameWithoutScores = match.games.first(where: { $0.scoreHistory.isEmpty }) {
                return gameWithoutScores
            } else if let lastGame = match.games.last, !lastGame.isCompleted {
                return lastGame
            } else {
                return nil
            }
        }
    }
}
