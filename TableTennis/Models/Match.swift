import Foundation
import CloudKit

/// Type wedstrijd: enkel, dubbel of mix & match
enum MatchType: String, CaseIterable, Codable {
    case singles = "singles"
    case doubles = "doubles"
    case mixMatch = "mixMatch"

    var displayName: String {
        switch self {
        case .singles: return "Singles"
        case .doubles: return "Doubles"
        case .mixMatch: return "Mix & Match Doubles"
        }
    }
}

/// Status van een wedstrijd
enum MatchStatus: String, Codable {
    case scheduled = "scheduled"
    case inProgress = "inProgress"
    case completed = "completed"
    case cancelled = "cancelled"
    
    var displayName: String {
        switch self {
        case .scheduled: return "Scheduled"
        case .inProgress: return "In Progress"
        case .completed: return "Completed"
        case .cancelled: return "Cancelled"
        }
    }
}

/// Model voor een tafeltennis wedstrijd
struct Match: Identifiable, Codable {
    let id: UUID
    var type: MatchType
    var status: MatchStatus
    var bestOfGames: Int // Aantal games (oneven getal: 1, 3, 5, etc.)
    var createdAt: Date
    var completedAt: Date?
    var competitionId: UUID // Referentie naar de competitie

    // Team 1 spelers
    var team1Player1: Player
    var team1Player2: Player? // Alleen voor dubbels

    // Team 2 spelers
    var team2Player1: Player
    var team2Player2: Player? // Alleen voor dubbels

    // Mix & Match spelers (4 spelers voor rotatie dubbel)
    var mixMatchPlayer3: Player? // Alleen voor mix & match
    var mixMatchPlayer4: Player? // Alleen voor mix & match

    // Games in deze wedstrijd
    var games: [Game] = []
    
    init(type: MatchType,
         competitionId: UUID,
         bestOfGames: Int = 3,
         team1Player1: Player,
         team1Player2: Player? = nil,
         team2Player1: Player,
         team2Player2: Player? = nil,
         mixMatchPlayer3: Player? = nil,
         mixMatchPlayer4: Player? = nil) {
        self.id = UUID()
        self.type = type
        self.status = .scheduled
        self.bestOfGames = bestOfGames
        self.competitionId = competitionId
        self.team1Player1 = team1Player1
        self.team1Player2 = team1Player2
        self.team2Player1 = team2Player1
        self.team2Player2 = team2Player2
        self.mixMatchPlayer3 = mixMatchPlayer3
        self.mixMatchPlayer4 = mixMatchPlayer4
        self.createdAt = Date()
    }

    /// Convenience initializer voor Mix & Match wedstrijden
    static func createMixMatch(players: [Player], competitionId: UUID) -> Match {
        guard players.count == 4 else {
            fatalError("Mix & Match vereist exact 4 spelers")
        }

        return Match(
            type: .mixMatch,
            competitionId: competitionId,
            bestOfGames: 3, // Altijd 3 games voor Mix & Match
            team1Player1: players[0],
            team1Player2: nil,
            team2Player1: players[1],
            team2Player2: nil,
            mixMatchPlayer3: players[2],
            mixMatchPlayer4: players[3]
        )
    }
    
    /// Berekent het gecombineerde ELO van team 1
    var team1EloRating: Double {
        if type == .mixMatch {
            // Voor Mix & Match wordt ELO per game berekend
            return 0
        }
        let player1Elo = team1Player1.eloRating
        let player2Elo = team1Player2?.eloRating ?? 0
        return type == .doubles ? (player1Elo + player2Elo) / 2 : player1Elo
    }

    /// Berekent het gecombineerde ELO van team 2
    var team2EloRating: Double {
        if type == .mixMatch {
            // Voor Mix & Match wordt ELO per game berekend
            return 0
        }
        let player1Elo = team2Player1.eloRating
        let player2Elo = team2Player2?.eloRating ?? 0
        return type == .doubles ? (player1Elo + player2Elo) / 2 : player1Elo
    }
    
    /// Geeft alle spelers in de wedstrijd terug
    var allPlayers: [Player] {
        if type == .mixMatch {
            var players = [team1Player1, team2Player1]
            if let mixMatchPlayer3 = mixMatchPlayer3 { players.append(mixMatchPlayer3) }
            if let mixMatchPlayer4 = mixMatchPlayer4 { players.append(mixMatchPlayer4) }
            return players
        } else {
            var players = [team1Player1, team2Player1]
            if let team1Player2 = team1Player2 { players.append(team1Player2) }
            if let team2Player2 = team2Player2 { players.append(team2Player2) }
            return players
        }
    }

    /// Geeft alle 4 spelers voor Mix & Match terug
    var mixMatchPlayers: [Player] {
        guard type == .mixMatch else { return [] }
        var players = [team1Player1, team2Player1]
        if let mixMatchPlayer3 = mixMatchPlayer3 { players.append(mixMatchPlayer3) }
        if let mixMatchPlayer4 = mixMatchPlayer4 { players.append(mixMatchPlayer4) }
        return players
    }
    
    /// Berekent hoeveel games team 1 heeft gewonnen
    var team1GamesWon: Int {
        return games.filter { $0.winner == .team1 }.count
    }
    
    /// Berekent hoeveel games team 2 heeft gewonnen
    var team2GamesWon: Int {
        return games.filter { $0.winner == .team2 }.count
    }
    
    /// Bepaalt de winnaar van de wedstrijd
    var winner: Team? {
        if type == .mixMatch {
            // Voor Mix & Match is er geen overall winnaar, alleen individuele game winnaars
            return games.count == 3 ? .team1 : nil // Dummy return als alle games gespeeld zijn
        }
        let gamesNeededToWin = (bestOfGames / 2) + 1
        if team1GamesWon >= gamesNeededToWin { return .team1 }
        if team2GamesWon >= gamesNeededToWin { return .team2 }
        return nil
    }

    /// Controleert of de wedstrijd is afgelopen
    var isCompleted: Bool {
        if type == .mixMatch {
            return games.count == 3 && games.allSatisfy { $0.isCompleted }
        }
        return winner != nil
    }
    
    /// Geeft een leesbare beschrijving van de wedstrijd
    var description: String {
        if type == .mixMatch {
            let playerNames = allPlayers.map { $0.name }.joined(separator: ", ")
            return playerNames
        }

        let team1Name = type == .doubles ?
            "\(team1Player1.name) & \(team1Player2?.name ?? "")" :
            team1Player1.name
        let team2Name = type == .doubles ?
            "\(team2Player1.name) & \(team2Player2?.name ?? "")" :
            team2Player1.name
        return "\(team1Name) vs \(team2Name)"
    }

    /// Genereert willekeurige team combinaties voor Mix & Match games
    /// Zorgt ervoor dat iedereen met iedereen speelt, maar in willekeurige volgorde en posities
    func getMixMatchTeamCombinations() -> [(team1: [Player], team2: [Player])] {
        guard type == .mixMatch,
              let player3 = mixMatchPlayer3,
              let player4 = mixMatchPlayer4 else {
            return []
        }

        let players = [team1Player1, team2Player1, player3, player4]

        // Alle mogelijke combinaties voor 4 spelers (A, B, C, D):
        // Combinatie 1: A+B vs C+D
        // Combinatie 2: A+C vs B+D
        // Combinatie 3: A+D vs B+C
        var allCombinations = [
            (team1: [players[0], players[1]], team2: [players[2], players[3]]), // A+B vs C+D
            (team1: [players[0], players[2]], team2: [players[1], players[3]]), // A+C vs B+D
            (team1: [players[0], players[3]], team2: [players[1], players[2]])  // A+D vs B+C
        ]

        // Shuffle de volgorde van de combinaties
        allCombinations.shuffle()

        // Shuffle ook de posities binnen elk team en tussen teams
        var randomizedCombinations: [(team1: [Player], team2: [Player])] = []

        for combination in allCombinations {
            var team1Players = combination.team1
            var team2Players = combination.team2

            // Shuffle spelers binnen elk team
            team1Players.shuffle()
            team2Players.shuffle()

            // Willekeurig bepalen welk team "team1" en welk team "team2" wordt
            if Bool.random() {
                randomizedCombinations.append((team1: team1Players, team2: team2Players))
            } else {
                randomizedCombinations.append((team1: team2Players, team2: team1Players))
            }
        }

        return randomizedCombinations
    }

    /// Geeft de team namen voor een specifieke Mix & Match game
    func getMixMatchGameDescription(gameIndex: Int) -> String {
        let combinations = getMixMatchTeamCombinations()
        guard gameIndex < combinations.count else { return "" }

        let combination = combinations[gameIndex]
        let team1Names = combination.team1.map { $0.name }.joined(separator: " & ")
        let team2Names = combination.team2.map { $0.name }.joined(separator: " & ")

        return "\(team1Names) vs \(team2Names)"
    }

    /// Genereert automatisch de 3 games voor Mix & Match met willekeurige team indelingen
    mutating func generateMixMatchGames() {
        guard type == .mixMatch else { return }

        let combinations = getMixMatchTeamCombinations()
        games = []

        for (index, combination) in combinations.enumerated() {
            let game = Game(
                gameNumber: index + 1,
                mixMatchTeam1Players: combination.team1,
                mixMatchTeam2Players: combination.team2
            )
            games.append(game)
        }
    }


}

// MARK: - CloudKit Support
extension Match {
    static let recordType = "Match"

    /// Initializer vanuit CloudKit CKRecord
    init?(from record: CKRecord) {
        guard let typeString = record["type"] as? String,
              let type = MatchType(rawValue: typeString),
              let statusString = record["status"] as? String,
              let status = MatchStatus(rawValue: statusString),
              let bestOfGames = record["bestOfGames"] as? Int,
              let createdAt = record["createdAt"] as? Date,
              let competitionIdString = record["competitionId"] as? String,
              let competitionId = UUID(uuidString: competitionIdString),
              let team1Player1Data = record["team1Player1"] as? Data,
              let team2Player1Data = record["team2Player1"] as? Data,
              let team1Player1 = try? JSONDecoder().decode(Player.self, from: team1Player1Data),
              let team2Player1 = try? JSONDecoder().decode(Player.self, from: team2Player1Data),
              let idString = record.recordID.recordName.components(separatedBy: "_").last,
              let id = UUID(uuidString: idString) else {
            return nil
        }

        self.id = id
        self.type = type
        self.status = status
        self.bestOfGames = bestOfGames
        self.createdAt = createdAt
        self.competitionId = competitionId
        self.completedAt = record["completedAt"] as? Date
        self.team1Player1 = team1Player1
        self.team2Player1 = team2Player1

        // Optional players
        if let team1Player2Data = record["team1Player2"] as? Data,
           let team1Player2 = try? JSONDecoder().decode(Player.self, from: team1Player2Data) {
            self.team1Player2 = team1Player2
        }

        if let team2Player2Data = record["team2Player2"] as? Data,
           let team2Player2 = try? JSONDecoder().decode(Player.self, from: team2Player2Data) {
            self.team2Player2 = team2Player2
        }

        if let mixMatchPlayer3Data = record["mixMatchPlayer3"] as? Data,
           let mixMatchPlayer3 = try? JSONDecoder().decode(Player.self, from: mixMatchPlayer3Data) {
            self.mixMatchPlayer3 = mixMatchPlayer3
        }

        if let mixMatchPlayer4Data = record["mixMatchPlayer4"] as? Data,
           let mixMatchPlayer4 = try? JSONDecoder().decode(Player.self, from: mixMatchPlayer4Data) {
            self.mixMatchPlayer4 = mixMatchPlayer4
        }

        // Games
        if let gamesData = record["games"] as? Data,
           let games = try? JSONDecoder().decode([Game].self, from: gamesData) {
            self.games = games
        } else {
            self.games = []
        }
    }

    /// Converteert naar CloudKit CKRecord
    func toCKRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: "Match_\(id.uuidString)")
        let record = CKRecord(recordType: Match.recordType, recordID: recordID)

        record["type"] = type.rawValue
        record["status"] = status.rawValue
        record["bestOfGames"] = bestOfGames
        record["createdAt"] = createdAt
        record["competitionId"] = competitionId.uuidString
        record["completedAt"] = completedAt

        // Encode players as Data
        if let team1Player1Data = try? JSONEncoder().encode(team1Player1) {
            record["team1Player1"] = team1Player1Data
        }
        if let team2Player1Data = try? JSONEncoder().encode(team2Player1) {
            record["team2Player1"] = team2Player1Data
        }
        if let team1Player2 = team1Player2,
           let team1Player2Data = try? JSONEncoder().encode(team1Player2) {
            record["team1Player2"] = team1Player2Data
        }
        if let team2Player2 = team2Player2,
           let team2Player2Data = try? JSONEncoder().encode(team2Player2) {
            record["team2Player2"] = team2Player2Data
        }
        if let mixMatchPlayer3 = mixMatchPlayer3,
           let mixMatchPlayer3Data = try? JSONEncoder().encode(mixMatchPlayer3) {
            record["mixMatchPlayer3"] = mixMatchPlayer3Data
        }
        if let mixMatchPlayer4 = mixMatchPlayer4,
           let mixMatchPlayer4Data = try? JSONEncoder().encode(mixMatchPlayer4) {
            record["mixMatchPlayer4"] = mixMatchPlayer4Data
        }

        // Encode games as Data
        if let gamesData = try? JSONEncoder().encode(games) {
            record["games"] = gamesData
        }

        return record
    }

    /// Converteert naar CloudKit CKRecord en update een bestaand record
    func toCKRecord(existingRecord: CKRecord) -> CKRecord {
        // Update het bestaande record met nieuwe data
        existingRecord["type"] = type.rawValue
        existingRecord["status"] = status.rawValue
        existingRecord["bestOfGames"] = bestOfGames
        existingRecord["createdAt"] = createdAt
        existingRecord["competitionId"] = competitionId.uuidString
        existingRecord["completedAt"] = completedAt

        // Encode players as Data
        if let team1Player1Data = try? JSONEncoder().encode(team1Player1) {
            existingRecord["team1Player1"] = team1Player1Data
        }
        if let team2Player1Data = try? JSONEncoder().encode(team2Player1) {
            existingRecord["team2Player1"] = team2Player1Data
        }
        if let team1Player2 = team1Player2,
           let team1Player2Data = try? JSONEncoder().encode(team1Player2) {
            existingRecord["team1Player2"] = team1Player2Data
        } else {
            existingRecord["team1Player2"] = nil
        }
        if let team2Player2 = team2Player2,
           let team2Player2Data = try? JSONEncoder().encode(team2Player2) {
            existingRecord["team2Player2"] = team2Player2Data
        } else {
            existingRecord["team2Player2"] = nil
        }
        if let mixMatchPlayer3 = mixMatchPlayer3,
           let mixMatchPlayer3Data = try? JSONEncoder().encode(mixMatchPlayer3) {
            existingRecord["mixMatchPlayer3"] = mixMatchPlayer3Data
        } else {
            existingRecord["mixMatchPlayer3"] = nil
        }
        if let mixMatchPlayer4 = mixMatchPlayer4,
           let mixMatchPlayer4Data = try? JSONEncoder().encode(mixMatchPlayer4) {
            existingRecord["mixMatchPlayer4"] = mixMatchPlayer4Data
        } else {
            existingRecord["mixMatchPlayer4"] = nil
        }

        // Encode games as Data
        if let gamesData = try? JSONEncoder().encode(games) {
            existingRecord["games"] = gamesData
        }

        return existingRecord
    }
}
