import Foundation
import CloudKit

/// Model voor een tafeltennis competitie
struct Competition: Identifiable, Codable, Hashable {
    let id: UUID
    var name: String
    var description: String
    var createdAt: Date
    var createdBy: String // iCloud user identifier
    var isActive: Bool
    
    init(name: String, description: String = "", createdBy: String) {
        self.id = UUID()
        self.name = name
        self.description = description
        self.createdAt = Date()
        self.createdBy = createdBy
        self.isActive = true
    }
}

// MARK: - CloudKit Support
extension Competition {
    static let recordType = "Competition"
    
    /// Initializer vanuit CloudKit CKRecord
    init?(from record: CKRecord) {
        guard let name = record["name"] as? String,
              let description = record["description"] as? String,
              let createdAt = record["createdAt"] as? Date,
              let createdBy = record["createdBy"] as? String,
              let isActive = record["isActive"] as? Bool,
              let idString = record.recordID.recordName.components(separatedBy: "_").last,
              let id = UUID(uuidString: idString) else {
            return nil
        }
        
        self.id = id
        self.name = name
        self.description = description
        self.createdAt = createdAt
        self.createdBy = createdBy
        self.isActive = isActive
    }
    
    /// Converteert naar CloudKit CKRecord
    func toCKRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: "Competition_\(id.uuidString)")
        let record = CKRecord(recordType: Competition.recordType, recordID: recordID)
        
        record["name"] = name
        record["description"] = description
        record["createdAt"] = createdAt
        record["createdBy"] = createdBy
        record["isActive"] = isActive
        
        return record
    }
}
