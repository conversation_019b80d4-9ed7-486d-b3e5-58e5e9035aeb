import Foundation
import CloudKit

/// Enum voor teams
enum Team: String, CaseIterable, Codable {
    case team1 = "team1"
    case team2 = "team2"
    
    var displayName: String {
        switch self {
        case .team1: return "Team 1"
        case .team2: return "Team 2"
        }
    }
    
    var opposite: Team {
        switch self {
        case .team1: return .team2
        case .team2: return .team1
        }
    }
}

/// Model voor een individuele game binnen een wedstrijd
struct Game: Identifiable, Codable {
    let id: UUID
    var gameNumber: Int
    var team1Score: Int {
        let count = scoreHistory.filter { $0.team == .team1 }.count
        print("🎯 TEAM1 SCORE CALC: scoreHistory has \(scoreHistory.count) entries, team1 has \(count) points")
        return count
    }
    var team2Score: Int {
        let count = scoreHistory.filter { $0.team == .team2 }.count
        print("🎯 TEAM2 SCORE CALC: scoreHistory has \(scoreHistory.count) entries, team2 has \(count) points")
        return count
    }
    var winner: Team?
    var isCompleted: Bool = false
    var startedAt: Date
    var completedAt: Date?

    // Service tracking
    var currentServer: Team = .team1
    var currentServerPlayer: Int = 1 // 1 of 2 voor dubbels
    var serviceChangesCount: Int = 0

    // Score history voor undo/redo
    var scoreHistory: [ScorePoint] = []

    // Mix & Match team informatie (alleen voor Mix & Match games)
    var mixMatchTeam1Players: [Player]? // Team 1 spelers voor deze specifieke game
    var mixMatchTeam2Players: [Player]? // Team 2 spelers voor deze specifieke game
    
    init(gameNumber: Int) {
        self.id = UUID()
        self.gameNumber = gameNumber
        self.startedAt = Date()
    }

    /// Initializer voor Mix & Match games met specifieke team samenstelling
    init(gameNumber: Int, mixMatchTeam1Players: [Player], mixMatchTeam2Players: [Player]) {
        self.id = UUID()
        self.gameNumber = gameNumber
        self.startedAt = Date()
        self.mixMatchTeam1Players = mixMatchTeam1Players
        self.mixMatchTeam2Players = mixMatchTeam2Players
    }
    
    /// Controleert of de game in extended play is (na 10-10)
    var isExtendedPlay: Bool {
        // Extended play is wanneer beide teams >= 10 punten hebben
        // Service wisselt dan elk punt, ongeacht of scores gelijk zijn
        let result = team1Score >= 10 && team2Score >= 10

        if result {
            print("🏓 EXTENDED PLAY: \(team1Score)-\(team2Score) - Service changes every point")
        }

        return result
    }

    /// Controleert of de game in deuce is (gelijke scores na 10-10)
    var isDeuce: Bool {
        return team1Score >= 10 && team2Score >= 10 && team1Score == team2Score
    }
    
    /// Controleert of een team heeft gewonnen
    var hasWinner: Bool {
        let minScore = 11
        let minDifference = 2
        
        if team1Score >= minScore && team1Score - team2Score >= minDifference {
            return true
        }
        if team2Score >= minScore && team2Score - team1Score >= minDifference {
            return true
        }
        return false
    }
    
    /// Bepaalt de winnaar van de game
    mutating func determineWinner() {
        if hasWinner {
            winner = team1Score > team2Score ? .team1 : .team2
            isCompleted = true
            completedAt = Date()
        }
    }
    
    /// Voegt een punt toe aan een team
    mutating func addPoint(to team: Team, matchType: MatchType, scoringPlayer: Int = 1) {
        let scorePoint = ScorePoint(
            team: team,
            scoringPlayer: scoringPlayer,
            team1ScoreBefore: team1Score,
            team2ScoreBefore: team2Score,
            serverBefore: currentServer,
            serverPlayerBefore: currentServerPlayer,
            timestamp: Date()
        )

        scoreHistory.append(scorePoint)

        // Scores worden nu automatisch berekend uit scoreHistory

        // Check for winner
        determineWinner()

        // Update service if game is not completed
        if !isCompleted {
            updateService(matchType: matchType)
        }
    }
    
    /// Update service volgens tafeltennis regels
    mutating func updateService(matchType: MatchType) {
        let oldServer = currentServer
        let oldServerPlayer = currentServerPlayer
        let currentScore = "\(team1Score)-\(team2Score)"

        print("🏓 SERVICE UPDATE: Game \(gameNumber), Score: \(currentScore), Type: \(matchType)")
        print("🏓 Before: \(oldServer) Player \(oldServerPlayer)")
        print("🏓 Is Extended Play (≥10-10): \(isExtendedPlay)")

        if isExtendedPlay {
            // Na 10-10 wisselt service elk punt - alleen tussen teams, niet tussen spelers
            print("🏓 EXTENDED PLAY MODE: Service changes every point after 10-10")
            if matchType == .doubles || matchType == .mixMatch {
                // Extended play: wissel alleen tussen teams, behoud dezelfde speler
                currentServer = currentServer.opposite
                // Speler blijft hetzelfde tijdens extended play
                print("🏓 EXTENDED: Switching from \(oldServer) Player \(currentServerPlayer) to \(currentServer) Player \(currentServerPlayer)")
            } else {
                currentServer = currentServer.opposite
            }
        } else {
            // Normale service wisseling: elke 2 punten
            serviceChangesCount += 1
            if serviceChangesCount % 2 == 0 {
                print("🏓 NORMAL MODE: Service changes every 2 points - CHANGING NOW (count: \(serviceChangesCount))")
                if matchType == .doubles || matchType == .mixMatch {
                    let nextServer = GameRulesEngine.nextDoublesServer(
                        currentServer: currentServer,
                        currentServerPlayer: currentServerPlayer,
                        gameNumber: gameNumber
                    )
                    currentServer = nextServer.team
                    currentServerPlayer = nextServer.player
                } else {
                    currentServer = currentServer.opposite
                }
            } else {
                print("🏓 NORMAL MODE: Service changes every 2 points - NOT CHANGING YET (count: \(serviceChangesCount))")
            }
        }

        print("🏓 After: \(currentServer) Player \(currentServerPlayer)")
    }
    
    /// Ongedaan maken van het laatste punt
    mutating func undoLastPoint() -> Bool {
        guard let lastPoint = scoreHistory.popLast() else { return false }
        
        // Scores worden automatisch berekend uit scoreHistory na het verwijderen van het punt
        
        // Herstel service
        currentServer = lastPoint.serverBefore
        currentServerPlayer = lastPoint.serverPlayerBefore
        serviceChangesCount -= 1
        
        // Reset winner status
        winner = nil
        isCompleted = false
        completedAt = nil
        
        return true
    }
}

/// Model voor een individueel punt in de score geschiedenis
struct ScorePoint: Identifiable, Codable {
    let id: UUID
    let team: Team
    let scoringPlayer: Int
    let team1ScoreBefore: Int
    let team2ScoreBefore: Int
    let serverBefore: Team
    let serverPlayerBefore: Int
    let timestamp: Date

    init(team: Team, scoringPlayer: Int, team1ScoreBefore: Int, team2ScoreBefore: Int, serverBefore: Team, serverPlayerBefore: Int, timestamp: Date) {
        self.id = UUID()
        self.team = team
        self.scoringPlayer = scoringPlayer
        self.team1ScoreBefore = team1ScoreBefore
        self.team2ScoreBefore = team2ScoreBefore
        self.serverBefore = serverBefore
        self.serverPlayerBefore = serverPlayerBefore
        self.timestamp = timestamp
    }
}

// MARK: - CloudKit Support
extension Game {
    static let recordType = "Game"

    /// Initializer vanuit CloudKit CKRecord
    init?(from record: CKRecord) {
        guard let gameNumber = record["gameNumber"] as? Int,
              let _ = record["team1Score"] as? Int, // Scores worden berekend uit scoreHistory
              let _ = record["team2Score"] as? Int, // Scores worden berekend uit scoreHistory
              let isCompleted = record["isCompleted"] as? Bool,
              let startedAt = record["startedAt"] as? Date,
              let currentServerString = record["currentServer"] as? String,
              let currentServer = Team(rawValue: currentServerString),
              let currentServerPlayer = record["currentServerPlayer"] as? Int,
              let serviceChangesCount = record["serviceChangesCount"] as? Int,
              let idString = record.recordID.recordName.components(separatedBy: "_").last,
              let id = UUID(uuidString: idString) else {
            return nil
        }

        self.id = id
        self.gameNumber = gameNumber
        // team1Score en team2Score worden automatisch berekend uit scoreHistory
        self.isCompleted = isCompleted
        self.startedAt = startedAt
        self.currentServer = currentServer
        self.currentServerPlayer = currentServerPlayer
        self.serviceChangesCount = serviceChangesCount

        // Optional fields
        self.completedAt = record["completedAt"] as? Date

        if let winnerString = record["winner"] as? String {
            self.winner = Team(rawValue: winnerString)
        }

        // Score history
        if let scoreHistoryData = record["scoreHistory"] as? Data,
           let scoreHistory = try? JSONDecoder().decode([ScorePoint].self, from: scoreHistoryData) {
            self.scoreHistory = scoreHistory
        } else {
            self.scoreHistory = []
        }

        // Mix & Match players
        if let mixMatchTeam1Data = record["mixMatchTeam1Players"] as? Data,
           let mixMatchTeam1Players = try? JSONDecoder().decode([Player].self, from: mixMatchTeam1Data) {
            self.mixMatchTeam1Players = mixMatchTeam1Players
        }

        if let mixMatchTeam2Data = record["mixMatchTeam2Players"] as? Data,
           let mixMatchTeam2Players = try? JSONDecoder().decode([Player].self, from: mixMatchTeam2Data) {
            self.mixMatchTeam2Players = mixMatchTeam2Players
        }
    }

    /// Converteert naar CloudKit CKRecord
    func toCKRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: "Game_\(id.uuidString)")
        let record = CKRecord(recordType: Game.recordType, recordID: recordID)

        record["gameNumber"] = gameNumber
        record["team1Score"] = team1Score
        record["team2Score"] = team2Score
        record["isCompleted"] = isCompleted
        record["startedAt"] = startedAt
        record["completedAt"] = completedAt
        record["currentServer"] = currentServer.rawValue
        record["currentServerPlayer"] = currentServerPlayer
        record["serviceChangesCount"] = serviceChangesCount
        record["winner"] = winner?.rawValue

        // Encode score history as Data
        if let scoreHistoryData = try? JSONEncoder().encode(scoreHistory) {
            record["scoreHistory"] = scoreHistoryData
        }

        // Encode Mix & Match players as Data
        if let mixMatchTeam1Players = mixMatchTeam1Players,
           let mixMatchTeam1Data = try? JSONEncoder().encode(mixMatchTeam1Players) {
            record["mixMatchTeam1Players"] = mixMatchTeam1Data
        }

        if let mixMatchTeam2Players = mixMatchTeam2Players,
           let mixMatchTeam2Data = try? JSONEncoder().encode(mixMatchTeam2Players) {
            record["mixMatchTeam2Players"] = mixMatchTeam2Data
        }

        return record
    }
}
