import Foundation
import CloudKit

/// Model voor een aanvraag om deel te nemen aan een competitie
struct CompetitionJoinRequest: Identifiable, Codable {
    let id: UUID
    let competitionId: UUID
    let requesterId: String // iCloud user identifier van aanvrager
    let requesterName: String // Na<PERSON> van <PERSON> aanvrager voor display
    let requestedAt: Date
    var status: JoinRequestStatus
    var reviewedAt: Date?
    var reviewedBy: String? // iCloud user identifier van reviewer (eigenaar)
    
    enum JoinRequestStatus: String, Codable, CaseIterable {
        case pending = "pending"
        case approved = "approved"
        case rejected = "rejected"
        
        var displayName: String {
            switch self {
            case .pending: return "Pending"
            case .approved: return "Approved"
            case .rejected: return "Rejected"
            }
        }
        
        var systemImage: String {
            switch self {
            case .pending: return "clock"
            case .approved: return "checkmark.circle.fill"
            case .rejected: return "xmark.circle.fill"
            }
        }
    }
    
    init(competitionId: UUID, requesterId: String, requesterName: String) {
        self.id = UUID()
        self.competitionId = competitionId
        self.requesterId = requesterId
        self.requesterName = requesterName
        self.requestedAt = Date()
        self.status = .pending
        self.reviewedAt = nil
        self.reviewedBy = nil
    }
}

// MARK: - CloudKit Support
extension CompetitionJoinRequest {
    static let recordType = "CompetitionJoinRequest"
    
    /// Initializer vanuit CloudKit CKRecord
    init?(from record: CKRecord) {
        guard let competitionIdString = record["competitionId"] as? String,
              let competitionId = UUID(uuidString: competitionIdString),
              let requesterId = record["requesterId"] as? String,
              let requesterName = record["requesterName"] as? String,
              let requestedAt = record["requestedAt"] as? Date,
              let statusString = record["status"] as? String,
              let status = JoinRequestStatus(rawValue: statusString),
              let idString = record.recordID.recordName.components(separatedBy: "_").last,
              let id = UUID(uuidString: idString) else {
            return nil
        }
        
        self.id = id
        self.competitionId = competitionId
        self.requesterId = requesterId
        self.requesterName = requesterName
        self.requestedAt = requestedAt
        self.status = status
        self.reviewedAt = record["reviewedAt"] as? Date
        self.reviewedBy = record["reviewedBy"] as? String
    }
    
    /// Converteert naar CloudKit CKRecord (voor nieuwe records)
    func toCKRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: "CompetitionJoinRequest_\(id.uuidString)")
        let record = CKRecord(recordType: CompetitionJoinRequest.recordType, recordID: recordID)

        record["competitionId"] = competitionId.uuidString
        record["requesterId"] = requesterId
        record["requesterName"] = requesterName
        record["requestedAt"] = requestedAt
        record["status"] = status.rawValue
        record["reviewedAt"] = reviewedAt
        record["reviewedBy"] = reviewedBy

        return record
    }

    /// Update een bestaand CloudKit CKRecord met de huidige waarden
    func updateCKRecord(_ record: CKRecord) {
        record["competitionId"] = competitionId.uuidString
        record["requesterId"] = requesterId
        record["requesterName"] = requesterName
        record["requestedAt"] = requestedAt
        record["status"] = status.rawValue
        record["reviewedAt"] = reviewedAt
        record["reviewedBy"] = reviewedBy
    }
}

// MARK: - Helper Methods
extension CompetitionJoinRequest {
    /// Markeert de aanvraag als goedgekeurd
    mutating func approve(by reviewerId: String) {
        self.status = .approved
        self.reviewedAt = Date()
        self.reviewedBy = reviewerId
    }
    
    /// Markeert de aanvraag als afgewezen
    mutating func reject(by reviewerId: String) {
        self.status = .rejected
        self.reviewedAt = Date()
        self.reviewedBy = reviewerId
    }
    
    /// Controleert of de aanvraag nog in behandeling is
    var isPending: Bool {
        return status == .pending
    }
    
    /// Controleert of de aanvraag is goedgekeurd
    var isApproved: Bool {
        return status == .approved
    }
    
    /// Controleert of de aanvraag is afgewezen
    var isRejected: Bool {
        return status == .rejected
    }
}
