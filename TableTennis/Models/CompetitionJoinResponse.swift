import Foundation
import CloudKit

/// Represents a response from a competition owner to a join request
struct CompetitionJoinResponse: Identifiable, Codable {
    let id: UUID
    let joinRequestId: UUID
    let competitionId: UUID
    let responderId: String // Owner who responded
    let responderName: String
    let decision: Decision
    let respondedAt: Date
    let message: String? // Optional message from owner
    
    enum Decision: String, CaseIterable, Codable {
        case approved = "approved"
        case rejected = "rejected"
    }
    
    static let recordType = "CompetitionJoinResponse"
    
    init(joinRequestId: UUID, competitionId: UUID, responderId: String, responderName: String, decision: Decision, message: String? = nil) {
        self.id = UUID()
        self.joinRequestId = joinRequestId
        self.competitionId = competitionId
        self.responderId = responderId
        self.responderName = responderName
        self.decision = decision
        self.respondedAt = Date()
        self.message = message
    }
    
    /// Initialize from CloudKit record
    init?(from record: CKRecord) {
        guard let idString = record["id"] as? String,
              let id = UUID(uuidString: idString),
              let joinRequestIdString = record["joinRequestId"] as? String,
              let joinRequestId = UUID(uuidString: joinRequestIdString),
              let competitionIdString = record["competitionId"] as? String,
              let competitionId = UUID(uuidString: competitionIdString),
              let responderId = record["responderId"] as? String,
              let responderName = record["responderName"] as? String,
              let decisionString = record["decision"] as? String,
              let decision = Decision(rawValue: decisionString),
              let respondedAt = record["respondedAt"] as? Date else {
            return nil
        }
        
        self.id = id
        self.joinRequestId = joinRequestId
        self.competitionId = competitionId
        self.responderId = responderId
        self.responderName = responderName
        self.decision = decision
        self.respondedAt = respondedAt
        self.message = record["message"] as? String
    }
    
    /// Convert to CloudKit record
    func toCKRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: "CompetitionJoinResponse_\(id.uuidString)")
        let record = CKRecord(recordType: CompetitionJoinResponse.recordType, recordID: recordID)
        
        record["id"] = id.uuidString
        record["joinRequestId"] = joinRequestId.uuidString
        record["competitionId"] = competitionId.uuidString
        record["responderId"] = responderId
        record["responderName"] = responderName
        record["decision"] = decision.rawValue
        record["respondedAt"] = respondedAt
        record["message"] = message
        
        return record
    }
    
    var isApproved: Bool {
        return decision == .approved
    }
    
    var isRejected: Bool {
        return decision == .rejected
    }
}
