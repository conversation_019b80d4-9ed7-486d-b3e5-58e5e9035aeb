import SwiftUI

struct EditMatchView: View {
    @EnvironmentObject var dataManager: DataManager
    @Environment(\.presentationMode) var presentationMode
    
    @State private var matchType: MatchType
    @State private var bestOfGames: Int
    @State private var matchStatus: MatchStatus
    
    // Team 1 spelers
    @State private var team1Player1: Player?
    @State private var team1Player2: Player?
    
    // Team 2 spelers
    @State private var team2Player1: Player?
    @State private var team2Player2: Player?
    
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var showingDeleteAlert = false
    
    let match: Match
    
    init(match: Match) {
        self.match = match
        self._matchType = State(initialValue: match.type)
        self._bestOfGames = State(initialValue: match.bestOfGames)
        self._matchStatus = State(initialValue: match.status)
        self._team1Player1 = State(initialValue: match.team1Player1)
        self._team1Player2 = State(initialValue: match.team1Player2)
        self._team2Player1 = State(initialValue: match.team2Player1)
        self._team2Player2 = State(initialValue: match.team2Player2)
    }
    
    var availablePlayers: [Player] {
        return dataManager.players.sorted { $0.name < $1.name }
    }
    
    var isValidConfiguration: Bool {
        guard let team1Player1 = team1Player1,
              let team2Player1 = team2Player1 else { return false }
        
        switch matchType {
        case .singles:
            return team1Player1.id != team2Player1.id
        case .doubles:
            guard let team1Player2 = team1Player2,
                  let team2Player2 = team2Player2 else { return false }

            let allPlayerIds = Set([team1Player1.id, team1Player2.id, team2Player1.id, team2Player2.id])
            return allPlayerIds.count == 4
        case .mixMatch:
            // Mix & Match heeft eigen validatie logica
            return true
        }
    }
    
    var canEditPlayers: Bool {
        return match.status == .scheduled || (match.status == .inProgress && match.games.isEmpty)
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Wedstrijd Informatie")) {
                    Picker("Status", selection: $matchStatus) {
                        ForEach([MatchStatus.scheduled, .inProgress, .completed, .cancelled], id: \.self) { status in
                            Text(status.displayName).tag(status)
                        }
                    }
                    .disabled(match.status == .completed)
                    
                    if canEditPlayers {
                        Picker("Type", selection: $matchType) {
                            ForEach(MatchType.allCases, id: \.self) { type in
                                Text(type.displayName).tag(type)
                            }
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        .onChange(of: matchType) { _ in
                            resetPlayerSelections()
                        }
                        
                        Stepper("Best of \(bestOfGames) games", value: $bestOfGames, in: 1...7, step: 2)
                    } else {
                        HStack {
                            Text("Type")
                            Spacer()
                            Text(matchType.displayName)
                                .foregroundColor(.secondary)
                        }
                        
                        HStack {
                            Text("Best of Games")
                            Spacer()
                            Text("\(bestOfGames)")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                if canEditPlayers {
                    Section(header: Text("Team 1")) {
                        PlayerPicker(
                            title: "Speler 1",
                            selectedPlayer: $team1Player1,
                            availablePlayers: availablePlayersForTeam1Player1
                        )
                        
                        if matchType == .doubles {
                            PlayerPicker(
                                title: "Speler 2",
                                selectedPlayer: $team1Player2,
                                availablePlayers: availablePlayersForTeam1Player2
                            )
                        }
                    }
                    
                    Section(header: Text("Team 2")) {
                        PlayerPicker(
                            title: "Speler 1",
                            selectedPlayer: $team2Player1,
                            availablePlayers: availablePlayersForTeam2Player1
                        )
                        
                        if matchType == .doubles {
                            PlayerPicker(
                                title: "Speler 2",
                                selectedPlayer: $team2Player2,
                                availablePlayers: availablePlayersForTeam2Player2
                            )
                        }
                    }
                } else {
                    Section(header: Text("Teams"), footer: Text("Teams kunnen niet meer worden gewijzigd nadat de wedstrijd is gestart.")) {
                        HStack {
                            Text("Team 1")
                            Spacer()
                            VStack(alignment: .trailing) {
                                Text(match.team1Player1.name)
                                if let team1Player2 = match.team1Player2 {
                                    Text(team1Player2.name)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                        
                        HStack {
                            Text("Team 2")
                            Spacer()
                            VStack(alignment: .trailing) {
                                Text(match.team2Player1.name)
                                if let team2Player2 = match.team2Player2 {
                                    Text(team2Player2.name)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                }
                
                if !match.games.isEmpty {
                    Section(header: Text("Wedstrijd Voortgang")) {
                        HStack {
                            Text("Games Gespeeld")
                            Spacer()
                            Text("\(match.games.count)")
                                .foregroundColor(.secondary)
                        }
                        
                        HStack {
                            Text("Huidige Stand")
                            Spacer()
                            Text("\(match.team1GamesWon) - \(match.team2GamesWon)")
                                .foregroundColor(.secondary)
                        }
                        
                        if match.status == .completed, let winner = match.winner {
                            HStack {
                                Text("Winnaar")
                                Spacer()
                                Text(winner.displayName)
                                    .foregroundColor(.green)
                                    .fontWeight(.medium)
                            }
                        }
                    }
                }
                
                Section(header: Text("Tijdstempels")) {
                    HStack {
                        Text("Aangemaakt")
                        Spacer()
                        Text(match.createdAt, formatter: dateTimeFormatter)
                            .foregroundColor(.secondary)
                    }
                    
                    if let completedAt = match.completedAt {
                        HStack {
                            Text("Voltooid")
                            Spacer()
                            Text(completedAt, formatter: dateTimeFormatter)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Section {
                    Button("Verwijder Wedstrijd") {
                        showingDeleteAlert = true
                    }
                    .foregroundColor(.red)
                }
            }
            .navigationTitle("Bewerk Wedstrijd")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Annuleren") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Opslaan") {
                        saveMatch()
                    }
                    .disabled(!isValidConfiguration)
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Fout"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
            .alert("Wedstrijd Verwijderen", isPresented: $showingDeleteAlert) {
                Button("Verwijderen", role: .destructive) {
                    deleteMatch()
                }
                Button("Annuleren", role: .cancel) { }
            } message: {
                Text("Weet je zeker dat je deze wedstrijd wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.")
            }
        }
    }
    
    // MARK: - Available Players Computed Properties
    
    var availablePlayersForTeam1Player1: [Player] {
        return availablePlayers
    }
    
    var availablePlayersForTeam1Player2: [Player] {
        return availablePlayers.filter { $0.id != team1Player1?.id }
    }
    
    var availablePlayersForTeam2Player1: [Player] {
        let excludedIds = Set([team1Player1?.id, team1Player2?.id].compactMap { $0 })
        return availablePlayers.filter { !excludedIds.contains($0.id) }
    }
    
    var availablePlayersForTeam2Player2: [Player] {
        let excludedIds = Set([team1Player1?.id, team1Player2?.id, team2Player1?.id].compactMap { $0 })
        return availablePlayers.filter { !excludedIds.contains($0.id) }
    }
    
    // MARK: - Helper Methods
    
    private func resetPlayerSelections() {
        team1Player2 = nil
        team2Player2 = nil
    }
    
    private func saveMatch() {
        guard let team1Player1 = team1Player1,
              let team2Player1 = team2Player1 else {
            alertMessage = "Selecteer alle benodigde spelers."
            showingAlert = true
            return
        }
        
        if matchType == .doubles && (team1Player2 == nil || team2Player2 == nil) {
            alertMessage = "Voor dubbelspel moeten beide teams 2 spelers hebben."
            showingAlert = true
            return
        }
        
        // Update match
        var updatedMatch = match
        updatedMatch.type = matchType
        updatedMatch.bestOfGames = bestOfGames
        updatedMatch.status = matchStatus
        updatedMatch.team1Player1 = team1Player1
        updatedMatch.team1Player2 = team1Player2
        updatedMatch.team2Player1 = team2Player1
        updatedMatch.team2Player2 = team2Player2
        
        // Als status wordt gewijzigd naar completed, zet completion date
        if matchStatus == .completed && match.status != .completed {
            updatedMatch.completedAt = Date()
        }
        
        dataManager.updateMatch(updatedMatch)
        presentationMode.wrappedValue.dismiss()
    }
    
    private func deleteMatch() {
        dataManager.deleteMatch(match)
        presentationMode.wrappedValue.dismiss()
    }
    
    private var dateTimeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter
    }
}

#Preview {
    EditMatchView(match: Match(
        type: .singles,
        competitionId: UUID(),
        team1Player1: Player(name: "Speler 1", competitionId: UUID()),
        team2Player1: Player(name: "Speler 2", competitionId: UUID())
    ))
    .environmentObject(DataManager())
}
