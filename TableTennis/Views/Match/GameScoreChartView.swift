import SwiftUI
import Charts

struct GameScoreChartView: View {
    let game: Game
    let match: Match
    @Environment(\.dismiss) private var dismiss
    
    private var chartData: [ScoreDataPoint] {
        var data: [ScoreDataPoint] = []
        var team1Score = 0
        var team2Score = 0
        
        // Start punt (0-0)
        data.append(ScoreDataPoint(pointNumber: 0, team1Score: 0, team2Score: 0))
        
        // Voeg elk punt toe
        for (index, scorePoint) in game.scoreHistory.enumerated() {
            if scorePoint.team == .team1 {
                team1Score += 1
            } else {
                team2Score += 1
            }
            
            data.append(ScoreDataPoint(
                pointNumber: index + 1,
                team1Score: team1Score,
                team2Score: team2Score
            ))
        }
        
        return data
    }
    
    private var team1Name: String {
        if match.type == .singles {
            return match.team1Player1.name
        } else {
            let player2Name = match.team1Player2?.name ?? ""
            return "\(match.team1Player1.name) & \(player2Name)"
        }
    }
    
    private var team2Name: String {
        if match.type == .singles {
            return match.team2Player1.name
        } else {
            let player2Name = match.team2Player2?.name ?? ""
            return "\(match.team2Player1.name) & \(player2Name)"
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header met game info
                    VStack(spacing: 12) {
                        Text("Game \(game.gameNumber)")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        HStack(spacing: 20) {
                            VStack {
                                Text(team1Name)
                                    .font(.headline)
                                    .foregroundColor(.blue)
                                    .multilineTextAlignment(.center)
                                Text("\(game.team1Score)")
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(.blue)
                            }
                            
                            Text("-")
                                .font(.title)
                                .foregroundColor(.secondary)
                            
                            VStack {
                                Text(team2Name)
                                    .font(.headline)
                                    .foregroundColor(.red)
                                    .multilineTextAlignment(.center)
                                Text("\(game.team2Score)")
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(.red)
                            }
                        }
                        
                        if game.isCompleted, let winner = game.winner {
                            Text("Winnaar: \(winner == .team1 ? team1Name : team2Name)")
                                .font(.subheadline)
                                .foregroundColor(winner == .team1 ? .blue : .red)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill((winner == .team1 ? Color.blue : Color.red).opacity(0.1))
                                )
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // Score verloop chart
                    if !chartData.isEmpty {
                        VStack(alignment: .leading, spacing: 16) {
                            Text("Scoreverloop")
                                .font(.headline)
                                .padding(.horizontal)
                            
                            Chart(chartData) { dataPoint in
                                LineMark(
                                    x: .value("Punt", dataPoint.pointNumber),
                                    y: .value("Score", dataPoint.team1Score)
                                )
                                .foregroundStyle(.blue)
                                .symbol(.circle)
                                .symbolSize(40)
                                
                                LineMark(
                                    x: .value("Punt", dataPoint.pointNumber),
                                    y: .value("Score", dataPoint.team2Score)
                                )
                                .foregroundStyle(.red)
                                .symbol(.square)
                                .symbolSize(40)
                            }
                            .frame(height: 300)
                            .chartXAxis {
                                AxisMarks(values: .automatic(desiredCount: 10)) { value in
                                    AxisGridLine()
                                    AxisTick()
                                    AxisValueLabel() {
                                        if let intValue = value.as(Int.self) {
                                            Text("\(intValue)")
                                                .font(.caption)
                                        }
                                    }
                                }
                            }
                            .chartYAxis {
                                AxisMarks(values: .automatic(desiredCount: 8)) { value in
                                    AxisGridLine()
                                    AxisTick()
                                    AxisValueLabel() {
                                        if let intValue = value.as(Int.self) {
                                            Text("\(intValue)")
                                                .font(.caption)
                                        }
                                    }
                                }
                            }
                            .chartLegend(position: .bottom) {
                                HStack(spacing: 20) {
                                    HStack(spacing: 8) {
                                        Circle()
                                            .fill(.blue)
                                            .frame(width: 12, height: 12)
                                        Text(team1Name)
                                            .font(.caption)
                                            .foregroundColor(.blue)
                                    }
                                    
                                    HStack(spacing: 8) {
                                        Rectangle()
                                            .fill(.red)
                                            .frame(width: 12, height: 12)
                                        Text(team2Name)
                                            .font(.caption)
                                            .foregroundColor(.red)
                                    }
                                }
                            }
                            .padding()
                            .background(Color(.systemBackground))
                            .cornerRadius(12)
                            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                        }
                    }
                    
                    // Punt voor punt overzicht
                    if !game.scoreHistory.isEmpty {
                        VStack(alignment: .leading, spacing: 16) {
                            Text("Punt voor punt")
                                .font(.headline)
                                .padding(.horizontal)
                            
                            LazyVStack(spacing: 8) {
                                ForEach(Array(game.scoreHistory.enumerated()), id: \.element.id) { index, scorePoint in
                                    PointDetailRow(
                                        pointNumber: index + 1,
                                        scorePoint: scorePoint,
                                        team1Name: team1Name,
                                        team2Name: team2Name
                                    )
                                }
                            }
                            .padding()
                            .background(Color(.systemBackground))
                            .cornerRadius(12)
                            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("Game Analyse")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Sluiten") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct ScoreDataPoint: Identifiable {
    let id = UUID()
    let pointNumber: Int
    let team1Score: Int
    let team2Score: Int
}

struct PointDetailRow: View {
    let pointNumber: Int
    let scorePoint: ScorePoint
    let team1Name: String
    let team2Name: String
    
    private var scoreAfterPoint: String {
        let team1Score = scorePoint.team1ScoreBefore + (scorePoint.team == .team1 ? 1 : 0)
        let team2Score = scorePoint.team2ScoreBefore + (scorePoint.team == .team2 ? 1 : 0)
        return "\(team1Score) - \(team2Score)"
    }
    
    var body: some View {
        HStack(spacing: 12) {
            // Punt nummer
            Text("\(pointNumber)")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
                .frame(width: 30, alignment: .leading)
            
            // Wie scoorde
            HStack(spacing: 4) {
                Circle()
                    .fill(scorePoint.team == .team1 ? Color.blue : Color.red)
                    .frame(width: 8, height: 8)
                
                Text(scorePoint.team == .team1 ? team1Name : team2Name)
                    .font(.caption)
                    .foregroundColor(scorePoint.team == .team1 ? .blue : .red)
                    .lineLimit(1)
            }
            .frame(width: 120, alignment: .leading)
            
            Spacer()
            
            // Score na punt
            Text(scoreAfterPoint)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.primary)
                .frame(width: 50, alignment: .trailing)
        }
        .padding(.vertical, 4)
        .padding(.horizontal, 8)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color(.systemGray6).opacity(0.5))
        )
    }
}

#Preview {
    let sampleMatch = Match(
        type: .singles,
        competitionId: UUID(),
        team1Player1: Player(name: "Joost", competitionId: UUID()),
        team2Player1: Player(name: "Darius", competitionId: UUID())
    )

    let sampleGame: Game = {
        var game = Game(gameNumber: 1)
        // Scores worden automatisch berekend uit scoreHistory
        game.isCompleted = true
        game.winner = .team1

        // Voeg wat sample score history toe
        game.scoreHistory = [
            ScorePoint(team: .team1, scoringPlayer: 1, team1ScoreBefore: 0, team2ScoreBefore: 0, serverBefore: .team1, serverPlayerBefore: 1, timestamp: Date()),
            ScorePoint(team: .team2, scoringPlayer: 1, team1ScoreBefore: 1, team2ScoreBefore: 0, serverBefore: .team1, serverPlayerBefore: 1, timestamp: Date()),
            ScorePoint(team: .team1, scoringPlayer: 1, team1ScoreBefore: 1, team2ScoreBefore: 1, serverBefore: .team2, serverPlayerBefore: 1, timestamp: Date())
        ]

        return game
    }()

    return GameScoreChartView(game: sampleGame, match: sampleMatch)
}
