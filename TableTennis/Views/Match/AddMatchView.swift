import SwiftUI

struct AddMatchView: View {
    @EnvironmentObject var dataManager: DataManager
    @EnvironmentObject var cloudKitManager: CloudKitManager
    @Environment(\.presentationMode) var presentationMode

    @State private var matchType: MatchType = .singles
    @State private var bestOfGames = 3
    @State private var playLive = false

    // Team 1 spelers
    @State private var team1Player1: Player?
    @State private var team1Player2: Player?

    // Team 2 spelers
    @State private var team2Player1: Player?
    @State private var team2Player2: Player?

    // Mix & Match spelers (4 spelers totaal)
    @State private var mixMatchPlayers: [Player?] = [nil, nil, nil, nil]

    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var showingLiveMatch = false
    @State private var createdMatch: Match?
    
    var availablePlayers: [Player] {
        return dataManager.players.sorted { $0.name < $1.name }
    }
    
    var isValidConfiguration: Bool {
        switch matchType {
        case .singles:
            guard let team1Player1 = team1Player1,
                  let team2Player1 = team2Player1 else { return false }
            return team1Player1.id != team2Player1.id

        case .doubles:
            guard let team1Player1 = team1Player1,
                  let team1Player2 = team1Player2,
                  let team2Player1 = team2Player1,
                  let team2Player2 = team2Player2 else { return false }

            let allPlayerIds = Set([team1Player1.id, team1Player2.id, team2Player1.id, team2Player2.id])
            return allPlayerIds.count == 4 // Alle spelers moeten uniek zijn

        case .mixMatch:
            let selectedPlayers = mixMatchPlayers.compactMap { $0 }
            guard selectedPlayers.count == 4 else { return false }

            let uniquePlayerIds = Set(selectedPlayers.map { $0.id })
            return uniquePlayerIds.count == 4 // Alle 4 spelers moeten uniek zijn
        }
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Match Type")) {
                    Picker("Type", selection: $matchType) {
                        ForEach(MatchType.allCases, id: \.self) { type in
                            Text(type.displayName).tag(type)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .onChange(of: matchType) { _ in
                        resetPlayerSelections()
                    }

                    if matchType != .mixMatch {
                        Stepper("Best of \(bestOfGames) games", value: $bestOfGames, in: 1...7, step: 2)
                    } else {
                        Text("Mix & Match always plays 3 games")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                if matchType == .mixMatch {
                    Section(header: Text("Players (4 players for rotation)")) {
                        ForEach(0..<4, id: \.self) { index in
                            PlayerPicker(
                                title: "Player \(index + 1)",
                                selectedPlayer: $mixMatchPlayers[index],
                                availablePlayers: availablePlayersForMixMatch(excludingIndex: index)
                            )
                        }

                        if isValidConfiguration {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Game Combinations:")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.secondary)

                                let players = mixMatchPlayers.compactMap { $0 }
                                if players.count == 4 {
                                    Text("Game 1: \(players[0].name) & \(players[1].name) vs \(players[2].name) & \(players[3].name)")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                    Text("Game 2: \(players[0].name) & \(players[2].name) vs \(players[1].name) & \(players[3].name)")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                    Text("Game 3: \(players[0].name) & \(players[3].name) vs \(players[1].name) & \(players[2].name)")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }
                            }
                            .padding(.top, 8)
                        }
                    }
                } else {
                    Section(header: Text("Team 1")) {
                        PlayerPicker(
                            title: "Player 1",
                            selectedPlayer: $team1Player1,
                            availablePlayers: availablePlayersForTeam1Player1
                        )

                        if matchType == .doubles {
                            PlayerPicker(
                                title: "Player 2",
                                selectedPlayer: $team1Player2,
                                availablePlayers: availablePlayersForTeam1Player2
                            )
                        }
                    }

                    Section(header: Text("Team 2")) {
                        PlayerPicker(
                            title: "Player 1",
                            selectedPlayer: $team2Player1,
                            availablePlayers: availablePlayersForTeam2Player1
                        )

                        if matchType == .doubles {
                            PlayerPicker(
                                title: "Player 2",
                                selectedPlayer: $team2Player2,
                                availablePlayers: availablePlayersForTeam2Player2
                            )
                        }
                    }
                }
                
                Section(header: Text("Options")) {
                    Toggle("Play Live", isOn: $playLive)

                    if playLive {
                        Text("The match will be started immediately in live mode")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    } else {
                        Text("The match will be saved and can be played later")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                if isValidConfiguration && matchType != .mixMatch {
                    Section(header: Text("Preview")) {
                        MatchPreviewView(
                            matchType: matchType,
                            team1Player1: team1Player1!,
                            team1Player2: team1Player2,
                            team2Player1: team2Player1!,
                            team2Player2: team2Player2,
                            bestOfGames: bestOfGames
                        )
                    }
                }
            }
            .navigationTitle("New Match")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(playLive ? "Start Live" : "Save") {
                        createMatch()
                    }
                    .disabled(!isValidConfiguration)
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Error"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
            .fullScreenCover(isPresented: $showingLiveMatch) {
                if let match = createdMatch {
                    NavigationView {
                        LiveMatchView(match: match)
                            .onDisappear {
                                // Sluit de AddMatchView wanneer de live match view wordt gesloten
                                presentationMode.wrappedValue.dismiss()
                            }
                    }
                }
            }
        }
    }
    
    // MARK: - Available Players Computed Properties
    
    var availablePlayersForTeam1Player1: [Player] {
        return availablePlayers
    }
    
    var availablePlayersForTeam1Player2: [Player] {
        return availablePlayers.filter { $0.id != team1Player1?.id }
    }
    
    var availablePlayersForTeam2Player1: [Player] {
        let excludedIds = Set([team1Player1?.id, team1Player2?.id].compactMap { $0 })
        return availablePlayers.filter { !excludedIds.contains($0.id) }
    }
    
    var availablePlayersForTeam2Player2: [Player] {
        let excludedIds = Set([team1Player1?.id, team1Player2?.id, team2Player1?.id].compactMap { $0 })
        return availablePlayers.filter { !excludedIds.contains($0.id) }
    }

    func availablePlayersForMixMatch(excludingIndex: Int) -> [Player] {
        let selectedPlayerIds = Set(mixMatchPlayers.enumerated().compactMap { index, player in
            index != excludingIndex ? player?.id : nil
        })
        return availablePlayers.filter { !selectedPlayerIds.contains($0.id) }
    }
    
    // MARK: - Helper Methods
    
    private func resetPlayerSelections() {
        team1Player1 = nil
        team1Player2 = nil
        team2Player1 = nil
        team2Player2 = nil
        mixMatchPlayers = [nil, nil, nil, nil]
    }
    
    private func createMatch() {
        guard let competition = cloudKitManager.currentCompetition else {
            alertMessage = "Geen competitie geselecteerd."
            showingAlert = true
            return
        }

        var newMatch: Match

        if matchType == .mixMatch {
            // Mix & Match validatie
            let selectedPlayers = mixMatchPlayers.compactMap { $0 }
            guard selectedPlayers.count == 4 else {
                alertMessage = "Selecteer alle 4 spelers voor Mix & Match."
                showingAlert = true
                return
            }

            // Maak Mix & Match wedstrijd aan
            newMatch = Match.createMixMatch(players: selectedPlayers, competitionId: competition.id)
            newMatch.generateMixMatchGames()

            // Voor Mix & Match: start met eerste game als live mode is geselecteerd
            if playLive {
                // Start de eerste game voor live mode
                if !newMatch.games.isEmpty {
                    newMatch.games[0] = GameRulesEngine.createMixMatchGame(
                        gameNumber: 1,
                        team1Players: newMatch.games[0].mixMatchTeam1Players ?? [],
                        team2Players: newMatch.games[0].mixMatchTeam2Players ?? []
                    )
                }
            }

        } else {
            // Reguliere wedstrijd validatie
            guard let team1Player1 = team1Player1,
                  let team2Player1 = team2Player1 else {
                alertMessage = "Selecteer alle benodigde spelers."
                showingAlert = true
                return
            }

            // Valideer dubbel configuratie
            if matchType == .doubles && (team1Player2 == nil || team2Player2 == nil) {
                alertMessage = "Voor dubbelspel moeten beide teams 2 spelers hebben."
                showingAlert = true
                return
            }

            // Maak reguliere wedstrijd aan
            newMatch = Match(
                type: matchType,
                competitionId: competition.id,
                bestOfGames: bestOfGames,
                team1Player1: team1Player1,
                team1Player2: team1Player2,
                team2Player1: team2Player1,
                team2Player2: team2Player2
            )

            if playLive {
                // Voeg eerste game toe voor reguliere wedstrijden
                let firstGame = GameRulesEngine.createNewGame(gameNumber: 1, matchType: matchType)
                newMatch.games = [firstGame]
            }
        }

        if playLive {
            newMatch.status = .inProgress
        }

        dataManager.addMatch(newMatch)

        if playLive {
            // Sla de aangemaakte wedstrijd op en toon live match view
            createdMatch = newMatch
            showingLiveMatch = true
        } else {
            // Sluit de view als we niet live spelen
            presentationMode.wrappedValue.dismiss()
        }
    }
}

struct PlayerPicker: View {
    let title: String
    @Binding var selectedPlayer: Player?
    let availablePlayers: [Player]
    
    var body: some View {
        HStack {
            Text(title)
            Spacer()
            
            if availablePlayers.isEmpty {
                Text("No players available")
                    .foregroundColor(.secondary)
            } else {
                Picker("", selection: $selectedPlayer) {
                    Text("Select player").tag(nil as Player?)
                    ForEach(availablePlayers) { player in
                        Text(player.name).tag(player as Player?)
                    }
                }
                .pickerStyle(MenuPickerStyle())
            }
        }
    }
}

struct MatchPreviewView: View {
    let matchType: MatchType
    let team1Player1: Player
    let team1Player2: Player?
    let team2Player1: Player
    let team2Player2: Player?
    let bestOfGames: Int
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Team 1")
                    .font(.subheadline)
                    .fontWeight(.medium)
                Spacer()
                Text("vs")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
                Text("Team 2")
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(team1Player1.name)
                        .font(.caption)
                    if let team1Player2 = team1Player2 {
                        Text(team1Player2.name)
                            .font(.caption)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text(team2Player1.name)
                        .font(.caption)
                    if let team2Player2 = team2Player2 {
                        Text(team2Player2.name)
                            .font(.caption)
                    }
                }
            }
            
            Divider()
            
            HStack {
                Text("Type: \(matchType.displayName)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("Best of \(bestOfGames)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

#Preview {
    AddMatchView()
        .environmentObject(DataManager())
}
