import SwiftUI
import CloudKit

struct CompetitionSelectionView: View {
    @EnvironmentObject var cloudKitManager: CloudKitManager
    @EnvironmentObject var joinRequestManager: JoinRequestManager
    @State private var availableCompetitions: [Competition] = []
    @State private var isLoading = false
    @State private var showingCreateCompetition = false
    @State private var errorMessage: String?
    @State private var retryCount = 0
    @State private var showingJoinRequest = false
    @State private var selectedCompetitionForJoin: Competition?
    @State private var showingJoinRequests = false
    @State private var selectedCompetitionForManage: Competition?
    @State private var showingAlert = false
    @State private var alertMessage = ""

    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Header
                VStack(spacing: 24) {
                    Spacer()

                    // App Title
                    Text("Table Tennis")
                        .font(.system(size: 32, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)

                    Text("Select Competition")
                        .font(.title2)
                        .foregroundColor(.secondary)

                    Spacer()
                }
                .frame(height: 200)
                .background(Color(.systemBackground))

                // Content
                VStack(spacing: 0) {
                    if isLoading {
                        VStack(spacing: 16) {
                            ProgressView()
                                .scaleEffect(1.2)

                            Text("Loading competitions...")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                    } else if !cloudKitManager.isSignedInToiCloud {
                        VStack(spacing: 20) {
                            Image(systemName: "icloud.slash")
                                .font(.system(size: 48))
                                .foregroundColor(.red)

                            Text("iCloud Required")
                                .font(.title2)
                                .fontWeight(.semibold)

                            Text("Please sign in to iCloud to access competitions")
                                .font(.body)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 40)

                            Button("Try Again") {
                                cloudKitManager.checkiCloudStatus()
                            }
                            .buttonStyle(PrimaryButtonStyle())
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                    } else {
                        // Competition List
                        ScrollView {
                            LazyVStack(spacing: 12) {
                                if availableCompetitions.isEmpty {
                                    VStack(spacing: 16) {
                                        Image(systemName: "trophy")
                                            .font(.system(size: 48))
                                            .foregroundColor(.secondary)

                                        Text("No competitions found")
                                            .font(.title3)
                                            .fontWeight(.semibold)

                                        Text("Create your first competition to get started")
                                            .font(.body)
                                            .foregroundColor(.secondary)
                                            .multilineTextAlignment(.center)

                                        VStack(spacing: 12) {
                                            Button("Refresh") {
                                                loadCompetitions()
                                            }
                                            .buttonStyle(SecondaryButtonStyle())

                                            Button("Debug Info") {
                                                debugCloudKitStatus()
                                            }
                                            .buttonStyle(SecondaryButtonStyle())
                                        }
                                    }
                                    .padding(.top, 60)
                                } else {
                                    ForEach(availableCompetitions) { competition in
                                        CompetitionRowView(
                                            competition: competition,
                                            onTap: { handleCompetitionTap(competition) }
                                        )
                                    }
                                }
                            }
                            .padding(.horizontal, 20)
                            .padding(.top, 20)
                        }
                    }
                }
                .background(Color(.systemGroupedBackground))

                // Bottom Button
                VStack {
                    Button(action: { showingCreateCompetition = true }) {
                        HStack {
                            Image(systemName: "plus")
                                .font(.headline)
                            Text("Create New Competition")
                                .font(.headline)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(Color.accentColor)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)
                }
                .background(Color(.systemBackground))
            }
            .navigationBarHidden(true)
            .onAppear {
                retryCount = 0
                attemptToLoadCompetitions()
            }
            .onChange(of: cloudKitManager.isSignedInToiCloud) { _, isSignedIn in
                if isSignedIn {
                    print("🔄 iCloud became available, loading competitions...")
                    loadCompetitions()
                }
            }
            .sheet(isPresented: $showingCreateCompetition) {
                CreateCompetitionView { competition in
                    availableCompetitions.insert(competition, at: 0)
                    selectCompetition(competition)
                }
            }
            .alert("Join Competition", isPresented: $showingJoinRequest) {
                Button("Request") {
                    if let competition = selectedCompetitionForJoin {
                        requestToJoinCompetition(competition)
                    }
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                if let competition = selectedCompetitionForJoin {
                    Text("Do you want to request to join '\(competition.name)'? The owner will review your request.")
                }
            }
            .alert("Notice", isPresented: $showingAlert) {
                Button("OK") { }
            } message: {
                Text(alertMessage)
            }
        }
    }

    // MARK: - Private Methods

    private func loadCompetitions() {
        print("🔄 loadCompetitions called")
        print("🔐 iCloud signed in: \(cloudKitManager.isSignedInToiCloud)")

        guard cloudKitManager.isSignedInToiCloud else {
            print("❌ Not signed in to iCloud, skipping load")
            return
        }

        isLoading = true
        errorMessage = nil

        print("📡 Starting to fetch competitions...")

        Task {
            do {
                let competitions = try await cloudKitManager.fetchAvailableCompetitions()
                print("✅ Successfully fetched \(competitions.count) competitions")
                print("📋 Competition names: \(competitions.map { $0.name })")
                await MainActor.run {
                    self.availableCompetitions = competitions
                    self.isLoading = false
                    print("🔄 UI updated with competitions")

                    // Extra debug info
                    if competitions.isEmpty {
                        print("⚠️ UI: No competitions to display")
                    } else {
                        print("✅ UI: Displaying \(competitions.count) competitions")
                    }
                }
            } catch {
                print("❌ Error fetching competitions: \(error)")
                print("❌ Error details: \(error.localizedDescription)")
                if let ckError = error as? CKError {
                    print("❌ CloudKit error code: \(ckError.code.rawValue)")
                    print("❌ CloudKit error description: \(ckError.localizedDescription)")
                }
                await MainActor.run {
                    self.errorMessage = "Fout bij laden competities: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }

    private func attemptToLoadCompetitions() {
        if cloudKitManager.isSignedInToiCloud {
            loadCompetitions()
        } else {
            retryCount += 1
            if retryCount <= 3 {
                print("⏳ iCloud not ready yet, retrying in \(retryCount) second(s)...")
                DispatchQueue.main.asyncAfter(deadline: .now() + Double(retryCount)) {
                    attemptToLoadCompetitions()
                }
            } else {
                print("❌ Max retries reached, iCloud still not available")
            }
        }
    }

    private func handleCompetitionTap(_ competition: Competition) {
        Task {
            // Check if the user has access
            let hasAccess = await cloudKitManager.hasAccessToCompetition(competition)
            let isOwner = await cloudKitManager.isCurrentUserOwner(of: competition)

            await MainActor.run {
                if hasAccess {
                    // User has access, select the competition
                    selectCompetition(competition)
                } else if isOwner {
                    // Owner always has access
                    selectCompetition(competition)
                } else {
                    // User has no access, show join request dialog
                    selectedCompetitionForJoin = competition
                    showingJoinRequest = true
                }
            }
        }
    }

    private func selectCompetition(_ competition: Competition) {
        cloudKitManager.selectCompetition(competition)
    }

    private func requestToJoinCompetition(_ competition: Competition) {
        Task {
            do {
                // Haal de echte gebruikersnaam op
                let userName = try await cloudKitManager.getCurrentUserName()
                try await cloudKitManager.requestToJoinCompetition(competition, requesterName: userName)

                // Update de badge count voor de eigenaar (als deze app ook door de eigenaar wordt gebruikt)
                joinRequestManager.refreshCount()

                await MainActor.run {
                    alertMessage = "Aanvraag verzonden! De eigenaar van de competitie zal je aanvraag beoordelen."
                    showingAlert = true
                }
            } catch {
                await MainActor.run {
                    alertMessage = error.localizedDescription
                    showingAlert = true
                }
            }
        }
    }

    private func debugCloudKitStatus() {
        print("🔍 DEBUG: CloudKit Status Check")
        print("🔐 iCloud signed in: \(cloudKitManager.isSignedInToiCloud)")
        print("🏆 Current competition: \(cloudKitManager.currentCompetition?.name ?? "None")")
        print("📊 Available competitions count: \(availableCompetitions.count)")
        print("⚠️ Error message: \(errorMessage ?? "None")")
        print("🔄 Is loading: \(isLoading)")

        // Force reload
        loadCompetitions()
    }
}

// MARK: - Custom Views

struct CompetitionRowView: View {
    let competition: Competition
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // Competition icon
                ZStack {
                    Circle()
                        .fill(Color.accentColor.opacity(0.1))
                        .frame(width: 48, height: 48)

                    Image(systemName: "trophy.fill")
                        .font(.title3)
                        .foregroundColor(.accentColor)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text(competition.name)
                        .font(.headline)
                        .foregroundColor(.primary)

                    if !competition.description.isEmpty {
                        Text(competition.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }

                    Text("Created \(competition.createdAt.formatted(date: .abbreviated, time: .omitted))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(16)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Button Styles

struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color.accentColor)
            .foregroundColor(.white)
            .cornerRadius(8)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .foregroundColor(.primary)
            .cornerRadius(8)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

#Preview {
    CompetitionSelectionView()
        .environmentObject(CloudKitManager.shared)
}
