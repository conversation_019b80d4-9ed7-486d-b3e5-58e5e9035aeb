import SwiftUI

struct CreateCompetitionView: View {
    @EnvironmentObject var cloudKitManager: CloudKitManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var competitionName = ""
    @State private var competitionDescription = ""
    @State private var isCreating = false
    @State private var errorMessage: String?
    
    let onCompetitionCreated: (Competition) -> Void
    
    var body: some View {
        NavigationStack {
            Form {
                Section {
                    TextField("Competition Name", text: $competitionName)
                        .textFieldStyle(RoundedBorderTextFieldStyle())

                    TextField("Description (optional)", text: $competitionDescription, axis: .vertical)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .lineLimit(3...6)
                } header: {
                    Text("Competition Details")
                } footer: {
                    Text("Give your competition a clear name so others can find it.")
                }
                
                Section {
                    VStack(alignment: .leading, spacing: 8) {
                        Label("Shared Competition", systemImage: "person.2.circle")
                            .font(.headline)

                        Text("This competition will be shared via iCloud. Other players can join by selecting the competition from the list.")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("• All participants can add players")
                            .font(.caption2)
                            .foregroundColor(.secondary)

                        Text("• All participants can create matches")
                            .font(.caption2)
                            .foregroundColor(.secondary)

                        Text("• Scores and statistics are automatically synchronized")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 4)
                } header: {
                    Text("How it works")
                }
                
                if let errorMessage = errorMessage {
                    Section {
                        Text(errorMessage)
                            .foregroundColor(.red)
                            .font(.caption)
                    }
                }
            }
            .navigationTitle("New Competition")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Create") {
                        createCompetition()
                    }
                    .disabled(competitionName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isCreating)
                }
            }
            .disabled(isCreating)
            .overlay {
                if isCreating {
                    Color.black.opacity(0.3)
                        .ignoresSafeArea()
                    
                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.5)
                        
                        Text("Creating competition...")
                            .font(.headline)
                    }
                    .padding(24)
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(radius: 10)
                }
            }
        }
    }
    
    private func createCompetition() {
        let trimmedName = competitionName.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedDescription = competitionDescription.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedName.isEmpty else { return }
        
        isCreating = true
        errorMessage = nil
        
        Task {
            do {
                let competition = try await cloudKitManager.createCompetition(
                    name: trimmedName,
                    description: trimmedDescription
                )
                
                await MainActor.run {
                    self.isCreating = false
                    self.onCompetitionCreated(competition)
                    self.dismiss()
                }
            } catch {
                await MainActor.run {
                    self.isCreating = false
                    self.errorMessage = "Error creating competition: \(error.localizedDescription)"
                }
            }
        }
    }
}

#Preview {
    CreateCompetitionView { _ in }
        .environmentObject(CloudKitManager.shared)
}
