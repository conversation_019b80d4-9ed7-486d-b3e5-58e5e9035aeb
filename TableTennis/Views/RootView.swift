import SwiftUI

struct RootView: View {
    @EnvironmentObject var cloudKitManager: CloudKitManager
    @EnvironmentObject var dataManager: DataManager
    @EnvironmentObject var joinRequestManager: JoinRequestManager
    @State private var isLoadingData = false
    
    var body: some View {
        Group {
            if cloudKitManager.currentCompetition == nil {
                // Toon competitie selectie
                CompetitionSelectionView()
            } else if isLoadingData {
                // Toon loading screen tijdens het laden van competitie data
                LoadingView()
            } else {
                // Toon hoofdapp
                ContentView()
            }
        }
        .onAppear {
            // Als er al een competitie is geselecteerd bij app start, laad dan de data
            if let competition = cloudKitManager.currentCompetition {
                print("🚀 RootView: Found existing competition on app start, loading data...")
                loadCompetitionData()
                joinRequestManager.startMonitoring(for: competition)
            }
        }
        .onChange(of: cloudKitManager.currentCompetition) { oldValue, newValue in
            print("🔄 RootView: Competition changed from \(oldValue?.name ?? "nil") to \(newValue?.name ?? "nil")")
            if let competition = newValue {
                loadCompetitionData()
                joinRequestManager.startMonitoring(for: competition)
            } else {
                joinRequestManager.stopMonitoring()
            }
        }
    }
    
    private func loadCompetitionData() {
        print("🚀 RootView: Starting to load competition data...")
        isLoadingData = true

        Task {
            await dataManager.loadData()
            await MainActor.run {
                isLoadingData = false
                print("✅ RootView: Competition data loading completed")
            }
        }
    }
}

struct LoadingView: View {
    @EnvironmentObject var cloudKitManager: CloudKitManager
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "trophy.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.blue)
            
            Text(cloudKitManager.currentCompetition?.name ?? "Competition")
                .font(.largeTitle)
                .fontWeight(.bold)

            ProgressView("Loading data...")
                .scaleEffect(1.2)

            Text("Players and matches are being loaded...")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
    }
}

#Preview {
    RootView()
        .environmentObject(CloudKitManager.shared)
        .environmentObject(DataManager())
}
