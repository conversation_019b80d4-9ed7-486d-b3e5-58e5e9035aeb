import SwiftUI

struct ConfettiView: View {
    @State private var confettiPieces: [ConfettiPiece] = []
    @State private var animationTimer: Timer?
    
    let colors: [Color] = [
        .red, .blue, .green, .yellow, .orange, .purple, .pink, .cyan
    ]
    
    var body: some View {
        ZStack {
            ForEach(confettiPieces, id: \.id) { piece in
                ConfettiPieceView(piece: piece)
            }
        }
        .onAppear {
            startConfetti()
        }
        .onDisappear {
            stopConfetti()
        }
    }
    
    private func startConfetti() {
        // Create initial burst of confetti
        createConfettiBurst()
        
        // Continue creating confetti pieces
        animationTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
            createConfettiPieces()
            removeOldPieces()
        }
    }
    
    private func stopConfetti() {
        animationTimer?.invalidate()
        animationTimer = nil
    }
    
    private func createConfettiBurst() {
        // Create a big initial burst
        for _ in 0..<50 {
            createConfettiPiece()
        }
    }
    
    private func createConfettiPieces() {
        // Create 2-3 new pieces every 0.1 seconds
        for _ in 0..<Int.random(in: 2...3) {
            createConfettiPiece()
        }
    }
    
    private func createConfettiPiece() {
        let piece = ConfettiPiece(
            id: UUID(),
            x: Double.random(in: 0...UIScreen.main.bounds.width),
            y: -20,
            color: colors.randomElement() ?? .blue,
            size: Double.random(in: 4...8),
            rotation: Double.random(in: 0...360),
            velocityX: Double.random(in: -50...50),
            velocityY: Double.random(in: 50...150),
            createdAt: Date()
        )
        confettiPieces.append(piece)
    }
    
    private func removeOldPieces() {
        let now = Date()
        confettiPieces.removeAll { piece in
            // Remove pieces that are too old or have fallen off screen
            let age = now.timeIntervalSince(piece.createdAt)
            let isOffScreen = piece.y > UIScreen.main.bounds.height + 50
            return age > 5.0 || isOffScreen
        }
    }
}

struct ConfettiPiece {
    let id: UUID
    var x: Double
    var y: Double
    let color: Color
    let size: Double
    var rotation: Double
    let velocityX: Double
    let velocityY: Double
    let createdAt: Date
}

struct ConfettiPieceView: View {
    @State private var piece: ConfettiPiece
    @State private var animationTimer: Timer?
    
    init(piece: ConfettiPiece) {
        self._piece = State(initialValue: piece)
    }
    
    var body: some View {
        Rectangle()
            .fill(piece.color)
            .frame(width: piece.size, height: piece.size)
            .rotationEffect(.degrees(piece.rotation))
            .position(x: piece.x, y: piece.y)
            .onAppear {
                startAnimation()
            }
            .onDisappear {
                stopAnimation()
            }
    }
    
    private func startAnimation() {
        animationTimer = Timer.scheduledTimer(withTimeInterval: 0.016, repeats: true) { _ in
            updatePosition()
        }
    }
    
    private func stopAnimation() {
        animationTimer?.invalidate()
        animationTimer = nil
    }
    
    private func updatePosition() {
        // Update position based on velocity
        piece.x += piece.velocityX * 0.016
        piece.y += piece.velocityY * 0.016
        
        // Add gravity
        piece.y += 200 * 0.016 * 0.016
        
        // Add rotation
        piece.rotation += 180 * 0.016
        
        // Add some wind effect
        piece.x += sin(piece.y * 0.01) * 10 * 0.016
    }
}

// Simplified confetti for better performance
struct SimpleConfettiView: View {
    @State private var isAnimating = false
    
    var body: some View {
        ZStack {
            ForEach(0..<30, id: \.self) { index in
                ConfettiShape()
                    .fill(confettiColor(for: index))
                    .frame(width: 6, height: 6)
                    .offset(
                        x: isAnimating ? Double.random(in: -200...200) : 0,
                        y: isAnimating ? Double.random(in: 300...600) : -50
                    )
                    .rotationEffect(.degrees(isAnimating ? Double.random(in: 0...720) : 0))
                    .animation(
                        .easeOut(duration: Double.random(in: 2...4))
                        .delay(Double.random(in: 0...0.5)),
                        value: isAnimating
                    )
            }
        }
        .onAppear {
            isAnimating = true
        }
    }
    
    private func confettiColor(for index: Int) -> Color {
        let colors: [Color] = [.red, .blue, .green, .yellow, .orange, .purple, .pink, .cyan]
        return colors[index % colors.count]
    }
}

struct ConfettiShape: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        // Create a small rectangle or circle
        if Bool.random() {
            // Rectangle
            path.addRect(rect)
        } else {
            // Circle
            path.addEllipse(in: rect)
        }
        
        return path
    }
}

#Preview {
    SimpleConfettiView()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black.opacity(0.1))
}
