import SwiftUI

struct ContentView: View {
    @EnvironmentObject var dataManager: DataManager
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            MatchListView()
                .tabItem {
                    Image(systemName: "list.bullet")
                    Text("Wedstrijden")
                }
                .tag(0)
            
            PlayerListView()
                .tabItem {
                    Image(systemName: "person.2")
                    Text("Spelers")
                }
                .tag(1)
            
            StatisticsView()
                .tabItem {
                    Image(systemName: "chart.bar")
                    Text("Statistieken")
                }
                .tag(2)
        }
    }
}

#Preview {
    ContentView()
        .environmentObject(DataManager())
}
