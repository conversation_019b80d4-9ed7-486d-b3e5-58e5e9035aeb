#!/usr/bin/env python3
import wave
import math
import os
import struct

def generate_tone(frequency, duration, sample_rate=44100, amplitude=0.3):
    """Generate a sine wave tone"""
    num_samples = int(sample_rate * duration)
    wave_data = []

    for i in range(num_samples):
        t = i / sample_rate
        # Add exponential decay envelope
        envelope = math.exp(-t * 3)
        sample = amplitude * math.sin(2 * math.pi * frequency * t) * envelope
        # Convert to 16-bit integer
        wave_data.append(int(sample * 32767))

    return wave_data

def save_wav(filename, audio_data, sample_rate=44100):
    """Save audio data as WAV file"""
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        # Pack the data as 16-bit signed integers
        packed_data = struct.pack('<' + 'h' * len(audio_data), *audio_data)
        wav_file.writeframes(packed_data)

def main():
    output_dir = "Table Tennis/Sounds"
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate point sound - short ping (800Hz, 0.1 seconds)
    point_sound = generate_tone(800, 0.1)
    save_wav(f"{output_dir}/point.wav", point_sound)
    print("Generated point.wav")
    
    # Generate game win sound - ascending tones
    game_win_data = []
    for freq in [440, 554, 659]:  # C, E, G chord
        tone = generate_tone(freq, 0.2)
        game_win_data.extend(tone)
    save_wav(f"{output_dir}/game_win.wav", game_win_data)
    print("Generated game_win.wav")

    # Generate match win sound - victory fanfare
    match_win_data = []
    victory_notes = [523, 659, 784, 1047]  # C, E, G, C (octave higher)
    for freq in victory_notes:
        tone = generate_tone(freq, 0.3)
        match_win_data.extend(tone)
    save_wav(f"{output_dir}/match_win.wav", match_win_data)
    print("Generated match_win.wav")
    
    print("All sound files generated successfully!")

if __name__ == "__main__":
    main()
